-- =====================================================
-- 电影推荐系统 MySQL 数据库初始化脚本
-- Movie Recommendation System Database Initialization
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS movie_recommendation 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE movie_recommendation;

-- =====================================================
-- 表结构定义 (Table Structure Definitions)
-- =====================================================

-- 1. 用户表 (Users Table)
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    bio TEXT,
    role ENUM('USER', 'ADMIN') NOT NULL DEFAULT 'USER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 电影表 (Movies Table)
CREATE TABLE IF NOT EXISTS movies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    director VARCHAR(255),
    actors TEXT,
    genre ENUM('ACTION', 'COMEDY', 'DRAMA', 'HORROR', 'ROMANCE', 'THRILLER', 
               'SCIENCE_FICTION', 'FANTASY', 'ANIMATION', 'DOCUMENTARY', 
               'CRIME', 'ADVENTURE', 'FAMILY', 'MYSTERY', 'WAR'),
    release_date DATE,
    duration INT COMMENT '电影时长（分钟）',
    description TEXT,
    poster_url VARCHAR(500),
    trailer_url VARCHAR(500),
    imdb_rating DECIMAL(3,1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_movies_title (title),
    INDEX idx_movies_genre (genre),
    INDEX idx_movies_release_date (release_date),
    INDEX idx_movies_imdb_rating (imdb_rating),
    INDEX idx_movies_director (director)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 评分表 (Ratings Table)
CREATE TABLE IF NOT EXISTS ratings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    movie_id BIGINT NOT NULL,
    rating DECIMAL(3,1) NOT NULL CHECK (rating >= 1.0 AND rating <= 10.0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    
    -- 唯一约束：每个用户对每部电影只能评分一次
    UNIQUE KEY uk_user_movie (user_id, movie_id),
    
    -- 索引
    INDEX idx_ratings_user_id (user_id),
    INDEX idx_ratings_movie_id (movie_id),
    INDEX idx_ratings_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 评论表 (Comments Table)
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    movie_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    likes INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_comments_user_id (user_id),
    INDEX idx_comments_movie_id (movie_id),
    INDEX idx_comments_likes (likes),
    INDEX idx_comments_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 示例数据插入 (Sample Data Insertion)
-- =====================================================

-- 清空现有数据（开发环境）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE comments;
TRUNCATE TABLE ratings;
TRUNCATE TABLE movies;
TRUNCATE TABLE users;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入示例用户数据
INSERT INTO users (username, email, password, avatar_url, bio, role) VALUES
('admin123', '<EMAIL>', '$2a$10$vSlCUls467jIE8bbZlEuHuEMmRm42zJlXG.3T4G2eFijVly.gJhua', 'https://via.placeholder.com/150', '系统管理员', 'ADMIN'),
('john_doe', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'https://via.placeholder.com/150', '电影爱好者，喜欢科幻和动作片', 'USER'),
('jane_smith', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'https://via.placeholder.com/150', '影评人，专注于剧情片分析', 'USER'),
('movie_lover', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'https://via.placeholder.com/150', '看遍所有经典电影的资深影迷', 'USER'),
('critic_master', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'https://via.placeholder.com/150', '专业影评人，关注独立电影', 'USER'),
('cinema_fan', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'https://via.placeholder.com/150', '热爱各种类型电影的观众', 'USER'),
('film_student', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'https://via.placeholder.com/150', '电影学院学生，研究电影理论', 'USER');

-- 插入示例电影数据
INSERT INTO movies (title, director, actors, genre, release_date, duration, description, poster_url, trailer_url, imdb_rating) VALUES
('肖申克的救赎', 'Frank Darabont', 'Tim Robbins, Morgan Freeman, Bob Gunton', 'DRAMA', '1994-09-23', 142, '被冤枉杀害妻子的银行家安迪在肖申克监狱中的救赎故事。这是一部关于希望、友谊和救赎的经典电影。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example1', 9.3),
('教父', 'Francis Ford Coppola', 'Marlon Brando, Al Pacino, James Caan', 'CRIME', '1972-03-24', 175, '意大利黑手党家族的传奇故事。讲述了维托·柯里昂家族的兴衰历程。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example2', 9.2),
('黑暗骑士', 'Christopher Nolan', 'Christian Bale, Heath Ledger, Aaron Eckhart', 'ACTION', '2008-07-18', 152, '蝙蝠侠与小丑的终极对决。希斯·莱杰的小丑表演成为经典。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example3', 9.0),
('阿甘正传', 'Robert Zemeckis', 'Tom Hanks, Robin Wright, Gary Sinise', 'DRAMA', '1994-07-06', 142, '智商只有75的阿甘的传奇人生。通过阿甘的视角回顾美国历史。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example4', 8.8),
('盗梦空间', 'Christopher Nolan', 'Leonardo DiCaprio, Marion Cotillard, Tom Hardy', 'SCIENCE_FICTION', '2010-07-16', 148, '在梦境中植入想法的科幻悬疑片。诺兰的又一部烧脑杰作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example5', 8.8),
('辛德勒的名单', 'Steven Spielberg', 'Liam Neeson, Ben Kingsley, Ralph Fiennes', 'DRAMA', '1993-12-15', 195, '二战期间辛德勒拯救犹太人的真实故事。斯皮尔伯格的历史巨作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example6', 8.9),
('泰坦尼克号', 'James Cameron', 'Leonardo DiCaprio, Kate Winslet, Billy Zane', 'ROMANCE', '1997-12-19', 194, '泰坦尼克号沉船事件中的爱情故事。卡梅隆的史诗级爱情片。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example7', 7.8),
('星球大战：新希望', 'George Lucas', 'Mark Hamill, Harrison Ford, Carrie Fisher', 'SCIENCE_FICTION', '1977-05-25', 121, '银河系中反抗军与帝国的斗争。开创了现代科幻电影的先河。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example8', 8.6),
('复仇者联盟：终局之战', 'Anthony Russo, Joe Russo', 'Robert Downey Jr., Chris Evans, Mark Ruffalo', 'ACTION', '2019-04-26', 181, '超级英雄们的最终决战。漫威电影宇宙的史诗级收官之作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example9', 8.4),
('寄生虫', 'Bong Joon-ho', 'Song Kang-ho, Lee Sun-kyun, Cho Yeo-jeong', 'THRILLER', '2019-05-30', 132, '韩国社会阶层差异的黑色幽默。奉俊昊导演的社会讽刺杰作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example10', 8.6),
('玩具总动员', 'John Lasseter', 'Tom Hanks, Tim Allen, Don Rickles', 'ANIMATION', '1995-11-22', 81, '玩具们的冒险故事。皮克斯的开山之作，开创了3D动画电影新时代。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example11', 8.3),
('狮子王', 'Roger Allers, Rob Minkoff', 'Matthew Broderick, Jeremy Irons, James Earl Jones', 'ANIMATION', '1994-06-24', 88, '小狮子辛巴的成长故事。迪士尼的经典动画电影。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example12', 8.5),
('惊魂记', 'Alfred Hitchcock', 'Anthony Perkins, Janet Leigh, Vera Miles', 'HORROR', '1960-06-16', 109, '希区柯克的经典恐怖悬疑片。开创了现代恐怖片的先河。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example13', 8.5),
('闪灵', 'Stanley Kubrick', 'Jack Nicholson, Shelley Duvall, Danny Lloyd', 'HORROR', '1980-05-23', 146, '酒店管理员的恐怖经历。库布里克的心理恐怖杰作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example14', 8.4),
('卡萨布兰卡', 'Michael Curtiz', 'Humphrey Bogart, Ingrid Bergman, Paul Henreid', 'ROMANCE', '1942-11-26', 102, '二战背景下的经典爱情故事。黑白电影时代的不朽经典。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example15', 8.5),
('低俗小说', 'Quentin Tarantino', 'John Travolta, Uma Thurman, Samuel L. Jackson', 'CRIME', '1994-10-14', 154, '昆汀·塔伦蒂诺的非线性叙事杰作。多线程故事结构的经典之作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example16', 8.9),
('指环王：护戒使者', 'Peter Jackson', 'Elijah Wood, Ian McKellen, Orlando Bloom', 'FANTASY', '2001-12-19', 178, '中土世界的史诗冒险开始。彼得·杰克逊的奇幻巨作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example17', 8.8),
('搏击俱乐部', 'David Fincher', 'Brad Pitt, Edward Norton, Helena Bonham Carter', 'THRILLER', '1999-10-15', 139, '关于现代社会异化的深刻思考。大卫·芬奇的心理惊悚片。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example18', 8.8),
('阿凡达', 'James Cameron', 'Sam Worthington, Zoe Saldana, Sigourney Weaver', 'SCIENCE_FICTION', '2009-12-18', 162, '潘多拉星球的视觉奇观。卡梅隆的3D技术革命之作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example19', 7.8),
('美丽人生', 'Roberto Benigni', 'Roberto Benigni, Nicoletta Braschi, Giorgio Cantarini', 'DRAMA', '1997-12-20', 116, '二战集中营中父亲保护儿子的感人故事。意大利电影的温情杰作。', 'https://via.placeholder.com/300x450', 'https://www.youtube.com/watch?v=example20', 8.6);

-- 插入示例评分数据
INSERT INTO ratings (user_id, movie_id, rating) VALUES
-- admin的评分
(1, 1, 9.5), (1, 2, 9.0), (1, 3, 8.8), (1, 6, 9.2),
-- john_doe的评分（喜欢科幻和动作片）
(2, 1, 9.0), (2, 3, 9.5), (2, 5, 9.2), (2, 8, 8.8), (2, 9, 8.5), (2, 19, 8.0),
-- jane_smith的评分（专注剧情片）
(3, 1, 9.8), (3, 2, 9.5), (3, 4, 9.0), (3, 6, 9.3), (3, 20, 8.8),
-- movie_lover的评分（资深影迷）
(4, 1, 9.5), (4, 2, 9.2), (4, 13, 8.5), (4, 14, 8.3), (4, 15, 8.7), (4, 16, 8.9),
-- critic_master的评分（专业影评人）
(5, 6, 9.5), (5, 10, 8.8), (5, 16, 9.0), (5, 17, 8.9), (5, 18, 8.7),
-- cinema_fan的评分
(6, 7, 7.5), (6, 11, 8.2), (6, 12, 8.4), (6, 19, 7.8),
-- film_student的评分
(7, 13, 8.6), (7, 14, 8.4), (7, 15, 8.5), (7, 18, 8.8);

-- 插入示例评论数据
INSERT INTO comments (user_id, movie_id, content, likes) VALUES
(2, 1, '这是我看过最好的电影之一！蒂姆·罗宾斯和摩根·弗里曼的表演太棒了。希望与救赎的主题深深打动了我。', 25),
(3, 1, '肖申克的救赎真的是经典中的经典，每次重看都有新的感悟。安迪的坚持和瑞德的友谊让人感动。', 18),
(4, 2, '教父三部曲中的第一部，马龙·白兰度的表演无可挑剔。家族、权力、背叛的主题处理得非常深刻。', 15),
(2, 3, '诺兰的杰作！希斯·莱杰的小丑角色令人印象深刻，完全颠覆了传统反派的形象。', 32),
(5, 3, '黑暗骑士重新定义了超级英雄电影的标准。不仅仅是动作片，更是一部深刻的社会寓言。', 28),
(3, 4, '阿甘正传让我们看到了美国历史的另一面，汤姆·汉克斯演技炸裂。简单的人生哲学却蕴含深刻道理。', 20),
(4, 5, '盗梦空间的剧情设计太精妙了，需要多看几遍才能完全理解。诺兰的想象力真的让人佩服。', 35),
(2, 5, '诺兰的想象力真的让人佩服，视觉效果也很震撼。梦境层次的设计堪称天才。', 22),
(5, 6, '辛德勒的名单是一部让人深思的电影，历史不应该被遗忘。斯皮尔伯格用电影记录了人性的光辉。', 26),
(3, 7, '泰坦尼克号的爱情故事虽然老套但依然感人。卡梅隆的视觉效果在当时是革命性的。', 12),
(4, 8, '星球大战开创了科幻电影的新纪元。卢卡斯构建的银河系至今仍在影响着流行文化。', 19),
(2, 9, '复联4是漫威宇宙的完美收官之作。十年布局的完美结局，让人热泪盈眶。', 40),
(5, 10, '寄生虫揭示了社会问题，奉俊昊导演功力深厚。黑色幽默中透露着深刻的社会批判。', 30),
(3, 11, '玩具总动员是皮克斯的开山之作，创意十足。开创了3D动画电影的新时代。', 16),
(4, 12, '狮子王的音乐和画面都很棒，童年回忆。迪士尼动画的巅峰之作。', 21),
(2, 13, '希区柯克的悬疑手法至今无人能及。每一个镜头都充满了紧张感。', 14),
(5, 14, '闪灵的恐怖氛围营造得非常好，库布里克的杰作。心理恐怖比血腥暴力更加可怕。', 17),
(3, 15, '卡萨布兰卡是黑白电影时代的经典之作。"Here\'s looking at you, kid"成为经典台词。', 13),
(6, 7, '虽然剧情有些俗套，但视觉效果确实震撼。杰克和露丝的爱情故事还是很感人的。', 8),
(7, 13, '希区柯克真是悬疑大师，每个镜头都经过精心设计。浴室那场戏至今仍是电影史经典。', 11),
(1, 1, '作为管理员，我必须说这部电影确实配得上9.3的高分。无论从剧情、表演还是主题都堪称完美。', 45);

-- =====================================================
-- 视图创建 (View Creation)
-- =====================================================

-- 电影统计视图
CREATE OR REPLACE VIEW movie_stats AS
SELECT 
    m.id,
    m.title,
    m.genre,
    m.release_date,
    m.imdb_rating,
    COUNT(DISTINCT r.id) as rating_count,
    ROUND(AVG(r.rating), 1) as average_rating,
    COUNT(DISTINCT c.id) as comment_count,
    COALESCE(SUM(c.likes), 0) as total_likes
FROM movies m
LEFT JOIN ratings r ON m.id = r.movie_id
LEFT JOIN comments c ON m.id = c.movie_id
GROUP BY m.id, m.title, m.genre, m.release_date, m.imdb_rating;

-- 用户活跃度视图
CREATE OR REPLACE VIEW user_activity AS
SELECT 
    u.id,
    u.username,
    u.role,
    COUNT(DISTINCT r.id) as ratings_count,
    COUNT(DISTINCT c.id) as comments_count,
    COALESCE(SUM(c.likes), 0) as total_likes_received
FROM users u
LEFT JOIN ratings r ON u.id = r.user_id
LEFT JOIN comments c ON u.id = c.user_id
GROUP BY u.id, u.username, u.role;

-- =====================================================
-- 注意：存储过程和触发器已移除
-- 原因：Spring Boot的data.sql通过JDBC执行，不支持DELIMITER语法
-- 如需使用存储过程和触发器，请在数据库客户端中单独执行
-- =====================================================

-- =====================================================
-- 初始化完成提示
-- =====================================================

SELECT 'Movie Recommendation System Database Initialized Successfully!' as Status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as movie_count FROM movies;
SELECT COUNT(*) as rating_count FROM ratings;
SELECT COUNT(*) as comment_count FROM comments;