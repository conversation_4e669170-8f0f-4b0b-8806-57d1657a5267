# 电影推荐系统 (Movie Recommendation System)

基于Spring Boot的电影推荐系统，提供用户注册登录、电影管理、评分评论、推荐算法等功能。

## 🎯 项目特性

- **用户管理**: 用户注册、登录、个人资料管理
- **电影管理**: 电影信息的增删改查、分类管理
- **评分系统**: 用户对电影进行评分
- **评论系统**: 用户对电影发表评论、点赞
- **推荐算法**: 基于用户行为的电影推荐
- **权限控制**: 基于JWT的身份认证和角色权限
- **API文档**: 集成Swagger UI的完整API文档
- **数据持久化**: 支持H2内存数据库和MySQL

## 🛠️ 技术栈

### 后端技术
- **框架**: Spring Boot 3.3.10
- **安全**: Spring Security + JWT
- **数据访问**: Spring Data JPA + Hibernate
- **数据库**: H2 (开发) / MySQL (生产)
- **API文档**: SpringDoc OpenAPI 3
- **构建工具**: Maven
- **Java版本**: JDK 17

### 主要依赖
- Spring Boot Starter Web
- Spring Boot Starter Data JPA
- Spring Boot Starter Security
- Spring Boot Starter Validation
- JWT (jjwt)
- H2 Database
- SpringDoc OpenAPI
- Apache Commons Lang3

## 📋 系统要求

- JDK 17 或更高版本
- Maven 3.6 或更高版本
- 内存: 最少 512MB
- 磁盘空间: 最少 100MB

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd movie-recommendation-system
```

### 2. 编译项目

```bash
mvn clean compile
```

### 3. 运行应用

```bash
mvn spring-boot:run
```

或者

```bash
mvn clean package
java -jar target/movie-recommendation-system-0.0.1-SNAPSHOT.jar
```

### 4. 访问应用

- **应用首页**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **H2控制台**: http://localhost:8080/h2-console
  - JDBC URL: `jdbc:h2:mem:moviedb`
  - 用户名: `sa`
  - 密码: `password`

## 📚 API文档

系统提供完整的RESTful API，主要包括以下模块：

### 认证模块 (`/api/auth`)
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/refresh` - 刷新Token
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/check-username` - 检查用户名可用性
- `GET /api/auth/check-email` - 检查邮箱可用性

### 用户模块 (`/api/users`)
- `GET /api/users/profile` - 获取当前用户信息
- `PUT /api/users/profile` - 更新用户资料
- `PUT /api/users/password` - 修改密码
- `GET /api/users/{userId}` - 获取用户信息
- `GET /api/users` - 获取用户列表（管理员）

### 电影模块 (`/api/movies`)
- `GET /api/movies` - 获取电影列表
- `GET /api/movies/{movieId}` - 获取电影详情
- `POST /api/movies` - 创建电影（管理员）
- `PUT /api/movies/{movieId}` - 更新电影（管理员）
- `DELETE /api/movies/{movieId}` - 删除电影（管理员）
- `GET /api/movies/search` - 搜索电影
- `GET /api/movies/popular` - 获取热门电影
- `GET /api/movies/latest` - 获取最新电影

### 评分模块 (`/api/ratings`)
- `POST /api/ratings` - 创建/更新评分
- `GET /api/ratings/user/{userId}` - 获取用户评分
- `GET /api/ratings/movie/{movieId}` - 获取电影评分
- `DELETE /api/ratings/{ratingId}` - 删除评分
- `GET /api/ratings/movie/{movieId}/average` - 获取电影平均评分

### 评论模块 (`/api/comments`)
- `POST /api/comments` - 创建评论
- `GET /api/comments/movie/{movieId}` - 获取电影评论
- `PUT /api/comments/{commentId}` - 更新评论
- `DELETE /api/comments/{commentId}` - 删除评论
- `POST /api/comments/{commentId}/like` - 点赞评论
- `DELETE /api/comments/{commentId}/like` - 取消点赞

## 🗄️ 数据库设计

### 主要实体

1. **User (用户)**
   - id, username, email, password
   - role, active, bio, avatarUrl
   - createdAt, updatedAt

2. **Movie (电影)**
   - id, title, description, director
   - genre, releaseDate, duration
   - posterUrl, createdAt, updatedAt

3. **Rating (评分)**
   - id, userId, movieId, score
   - createdAt, updatedAt

4. **Comment (评论)**
   - id, userId, movieId, content
   - likes, createdAt, updatedAt

### 关系设计
- User ↔ Rating (一对多)
- User ↔ Comment (一对多)
- Movie ↔ Rating (一对多)
- Movie ↔ Comment (一对多)

## 🔧 配置说明

### 应用配置 (`application.yml`)

```yaml
# 数据库配置
spring:
  datasource:
    url: jdbc:h2:mem:moviedb
    username: sa
    password: password

# JWT配置
jwt:
  secret: movieRecommendationSystemSecretKey2025
  expiration: 86400000  # 24小时

# 服务器配置
server:
  port: 8080
```

### 环境配置

支持多环境配置：
- `dev` - 开发环境（默认）
- `test` - 测试环境
- `prod` - 生产环境

切换环境：
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

## 🧪 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

### 生成测试报告
```bash
mvn jacoco:report
```

## 📦 部署

### 构建生产包
```bash
mvn clean package -Pprod
```

### Docker部署（可选）
```bash
# 构建镜像
docker build -t movie-recommendation-system .

# 运行容器
docker run -p 8080:8080 movie-recommendation-system
```

## 🔒 安全配置

### JWT认证
- 使用JWT进行无状态身份认证
- Token有效期：24小时
- 刷新Token有效期：7天

### 角色权限
- `USER`: 普通用户权限
- `ADMIN`: 管理员权限

### 密码安全
- 使用BCrypt加密存储
- 密码长度：6-20位
- 支持密码强度验证

## 📊 监控和日志

### 应用监控
- Spring Boot Actuator
- 健康检查：`/actuator/health`
- 应用信息：`/actuator/info`
- 指标监控：`/actuator/metrics`

### 日志配置
- 开发环境：控制台输出
- 生产环境：文件输出
- 日志级别可配置

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：Movie Recommendation Team
- 邮箱：<EMAIL>
- 项目地址：https://github.com/movie-recommendation/system

## 🙏 致谢

感谢以下开源项目：
- Spring Boot
- Spring Security
- Spring Data JPA
- JWT
- H2 Database
- SpringDoc OpenAPI

---

**注意**: 这是一个教学项目，用于Web应用开发课程。在生产环境中使用前，请确保进行充分的安全审查和性能测试。