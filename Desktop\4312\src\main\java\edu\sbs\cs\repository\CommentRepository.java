package edu.sbs.cs.repository;

import edu.sbs.cs.entity.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论数据访问接口
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {
    
    /**
     * 根据电影ID查找评论
     * @param movieId 电影ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByMovieId(Long movieId, Pageable pageable);
    
    /**
     * 根据用户ID查找评论
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据电影ID查找评论（不分页）
     * @param movieId 电影ID
     * @return 评论列表
     */
    List<Comment> findByMovieId(Long movieId);
    
    /**
     * 根据用户ID查找评论（不分页）
     * @param userId 用户ID
     * @return 评论列表
     */
    List<Comment> findByUserId(Long userId);
    
    /**
     * 根据电影ID按点赞数排序查找评论
     * @param movieId 电影ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByMovieIdOrderByLikesDesc(Long movieId, Pageable pageable);
    
    /**
     * 根据电影ID按创建时间排序查找评论
     * @param movieId 电影ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByMovieIdOrderByCreatedAtDesc(Long movieId, Pageable pageable);
    
    /**
     * 统计电影的评论数量
     * @param movieId 电影ID
     * @return 评论数量
     */
    Long countByMovieId(Long movieId);
    
    /**
     * 统计用户的评论数量
     * @param userId 用户ID
     * @return 评论数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 根据内容关键词搜索评论
     * @param keyword 关键词
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    @Query("SELECT c FROM Comment c WHERE LOWER(c.content) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Comment> searchByContent(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据内容关键词搜索评论（忽略大小写）
     * @param keyword 关键词
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByContentContainingIgnoreCase(String keyword, Pageable pageable);
    
    /**
     * 根据电影ID和内容关键词搜索评论
     * @param movieId 电影ID
     * @param content 内容关键词
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByMovieIdAndContentContainingIgnoreCase(Long movieId, String content, Pageable pageable);
    
    /**
     * 根据时间范围查找评论
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据时间范围统计评论数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评论数量
     */
    Long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取热门评论（点赞数高的评论）
     * @param minLikes 最少点赞数
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    @Query("SELECT c FROM Comment c WHERE c.likes >= :minLikes ORDER BY c.likes DESC, c.createdAt DESC")
    Page<Comment> findPopularComments(@Param("minLikes") Integer minLikes, Pageable pageable);
    
    /**
     * 获取热门评论（默认最少点赞数）
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    @Query("SELECT c FROM Comment c ORDER BY c.likes DESC, c.createdAt DESC")
    Page<Comment> findPopularComments(Pageable pageable);
    
    /**
     * 获取最新评论
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 获取最新评论
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    @Query("SELECT c FROM Comment c ORDER BY c.createdAt DESC")
    Page<Comment> findLatestComments(Pageable pageable);
    
    /**
     * 根据用户ID和电影ID查找评论
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 评论列表
     */
    List<Comment> findByUserIdAndMovieId(Long userId, Long movieId);
    
    /**
     * 根据用户ID和电影ID查找评论（分页）
     * @param userId 用户ID
     * @param movieId 电影ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByUserIdAndMovieId(Long userId, Long movieId, Pageable pageable);
    
    /**
     * 根据用户ID按创建时间排序查找评论
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    Page<Comment> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    /**
     * 获取电影的热门评论（按点赞数排序，限制数量）
     * @param movieId 电影ID
     * @param limit 限制数量
     * @return 评论列表
     */
    @Query(value = "SELECT * FROM comments WHERE movie_id = :movieId ORDER BY likes DESC LIMIT :limit", nativeQuery = true)
    List<Comment> findTopCommentsByMovieId(@Param("movieId") Long movieId, @Param("limit") int limit);
    
    /**
     * 获取电影的热门评论（分页）
     * @param movieId 电影ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    @Query("SELECT c FROM Comment c WHERE c.movie.id = :movieId ORDER BY c.likes DESC, c.createdAt DESC")
    Page<Comment> findTopCommentsByMovie(@Param("movieId") Long movieId, Pageable pageable);
    
    /**
     * 获取用户最近的评论
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 评论列表
     */
    @Query(value = "SELECT * FROM comments WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit", nativeQuery = true)
    List<Comment> findRecentCommentsByUserId(@Param("userId") Long userId, @Param("limit") int limit);
    
    /**
     * 获取用户最近的评论（分页）
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 评论分页列表
     */
    @Query("SELECT c FROM Comment c WHERE c.user.id = :userId ORDER BY c.createdAt DESC")
    Page<Comment> findRecentCommentsByUser(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 统计总评论数
     * @return 评论总数
     */
    @Query("SELECT COUNT(c) FROM Comment c")
    Long countComments();
    
    /**
     * 获取评论统计信息（总数、平均点赞数等）
     * @return 统计信息数组
     */
    @Query("SELECT COUNT(c), AVG(c.likes), MAX(c.likes), MIN(c.likes) FROM Comment c")
    Object[] findCommentStatistics();
    
    /**
     * 根据电影ID删除所有评论
     * @param movieId 电影ID
     */
    void deleteByMovieId(Long movieId);
    
    /**
     * 根据用户ID删除所有评论
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
    
    /**
     * 计算平均点赞数
     * @return 平均点赞数
     */
    @Query("SELECT AVG(c.likes) FROM Comment c")
    Double findAverageLikes();
    
    /**
     * 查找最高点赞数
     * @return 最高点赞数
     */
    @Query("SELECT MAX(c.likes) FROM Comment c")
    Integer findMaxLikes();
    
    /**
     * 计算用户评论的总点赞数
     * @param userId 用户ID
     * @return 总点赞数
     */
    @Query("SELECT SUM(c.likes) FROM Comment c WHERE c.user.id = :userId")
    Long sumLikesByUserId(@Param("userId") Long userId);
    
    /**
     * 查找用户评论的最高点赞数
     * @param userId 用户ID
     * @return 最高点赞数
     */
    @Query("SELECT MAX(c.likes) FROM Comment c WHERE c.user.id = :userId")
    Integer findMaxLikesByUserId(@Param("userId") Long userId);
    
    /**
     * 计算电影评论的总点赞数
     * @param movieId 电影ID
     * @return 总点赞数
     */
    @Query("SELECT SUM(c.likes) FROM Comment c WHERE c.movie.id = :movieId")
    Long sumLikesByMovieId(@Param("movieId") Long movieId);
    
    /**
     * 查找电影评论的最高点赞数
     * @param movieId 电影ID
     * @return 最高点赞数
     */
    @Query("SELECT MAX(c.likes) FROM Comment c WHERE c.movie.id = :movieId")
    Integer findMaxLikesByMovieId(@Param("movieId") Long movieId);
    
    /**
     * 检查用户是否对电影发表过评论
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 是否存在评论
     */
    boolean existsByUserIdAndMovieId(Long userId, Long movieId);
}