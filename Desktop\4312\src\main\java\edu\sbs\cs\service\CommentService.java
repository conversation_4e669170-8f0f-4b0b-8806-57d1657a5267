package edu.sbs.cs.service;

import edu.sbs.cs.entity.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 评论服务接口
 * 定义评论相关的业务逻辑方法
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
public interface CommentService {
    
    /**
     * 创建评论
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @param content 评论内容
     * @return 评论实体
     */
    Comment createComment(Long userId, Long movieId, String content);
    
    /**
     * 根据ID查找评论
     * 
     * @param commentId 评论ID
     * @return 评论实体（可选）
     */
    Optional<Comment> findById(Long commentId);
    
    /**
     * 根据电影ID获取评论列表（分页）
     * 
     * @param movieId 电影ID
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByMovieId(Long movieId, Pageable pageable);
    
    /**
     * 根据用户ID获取评论列表（分页）
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据电影ID获取评论列表，按点赞数排序
     * 
     * @param movieId 电影ID
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByMovieIdOrderByLikes(Long movieId, Pageable pageable);
    
    /**
     * 根据电影ID获取评论列表，按创建时间排序
     * 
     * @param movieId 电影ID
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByMovieIdOrderByCreatedAt(Long movieId, Pageable pageable);
    
    /**
     * 根据用户ID获取评论列表，按创建时间排序
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByUserIdOrderByCreatedAt(Long userId, Pageable pageable);
    
    /**
     * 获取电影的评论数量
     * 
     * @param movieId 电影ID
     * @return 评论数量
     */
    Long countByMovieId(Long movieId);
    
    /**
     * 获取用户的评论数量
     * 
     * @param userId 用户ID
     * @return 评论数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 根据关键词搜索评论
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> searchByContent(String keyword, Pageable pageable);
    
    /**
     * 根据时间范围查找评论
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 获取热门评论（按点赞数排序）
     * 
     * @param pageable 分页参数
     * @return 热门评论分页列表
     */
    Page<Comment> findPopularComments(Pageable pageable);
    
    /**
     * 获取最新评论
     * 
     * @param pageable 分页参数
     * @return 最新评论分页列表
     */
    Page<Comment> findLatestComments(Pageable pageable);
    
    /**
     * 根据用户ID和电影ID查找评论
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> findByUserIdAndMovieId(Long userId, Long movieId, Pageable pageable);
    
    /**
     * 获取电影的热门评论
     * 
     * @param movieId 电影ID
     * @param pageable 分页参数
     * @return 热门评论分页列表
     */
    Page<Comment> findTopCommentsByMovie(Long movieId, Pageable pageable);
    
    /**
     * 获取用户最近的评论
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 最近评论分页列表
     */
    Page<Comment> findRecentCommentsByUser(Long userId, Pageable pageable);
    
    /**
     * 更新评论内容
     * 
     * @param commentId 评论ID
     * @param content 新内容
     * @return 更新后的评论
     */
    Comment updateComment(Long commentId, String content);
    
    /**
     * 点赞评论
     * 
     * @param commentId 评论ID
     * @return 更新后的评论
     */
    Comment likeComment(Long commentId);
    
    /**
     * 取消点赞评论
     * 
     * @param commentId 评论ID
     * @return 更新后的评论
     */
    Comment unlikeComment(Long commentId);
    
    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @return 是否删除成功
     */
    boolean deleteComment(Long commentId);
    
    /**
     * 根据电影ID删除所有评论
     * 
     * @param movieId 电影ID
     * @return 删除的评论数量
     */
    Long deleteByMovieId(Long movieId);
    
    /**
     * 根据用户ID删除所有评论
     * 
     * @param userId 用户ID
     * @return 删除的评论数量
     */
    Long deleteByUserId(Long userId);
    
    /**
     * 获取评论统计信息
     * 
     * @return 评论统计信息
     */
    Map<String, Object> getCommentStats();
    
    /**
     * 获取用户的评论统计信息
     * 
     * @param userId 用户ID
     * @return 用户评论统计信息
     */
    Map<String, Object> getUserCommentStats(Long userId);
    
    /**
     * 获取电影的评论统计信息
     * 
     * @param movieId 电影ID
     * @return 电影评论统计信息
     */
    Map<String, Object> getMovieCommentStats(Long movieId);
    
    /**
     * 检查用户是否已对电影评论
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 是否已评论
     */
    boolean hasUserCommentedOnMovie(Long userId, Long movieId);
    
    /**
     * 获取所有评论数量
     * 
     * @return 总评论数量
     */
    Long getTotalCommentCount();
    
    /**
     * 批量创建评论
     * 
     * @param comments 评论列表
     * @return 创建的评论列表
     */
    List<Comment> createComments(List<Comment> comments);
    
    /**
     * 根据电影ID和内容关键词搜索评论
     * 
     * @param movieId 电影ID
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 评论分页列表
     */
    Page<Comment> searchByMovieIdAndContent(Long movieId, String keyword, Pageable pageable);
    
    /**
     * 获取评论的回复数量（如果支持回复功能）
     * 
     * @param commentId 评论ID
     * @return 回复数量
     */
    Long getReplyCount(Long commentId);
    
    /**
     * 检查用户是否有权限删除评论
     * 
     * @param userId 用户ID
     * @param commentId 评论ID
     * @return 是否有权限
     */
    boolean canUserDeleteComment(Long userId, Long commentId);
    
    /**
     * 检查用户是否有权限编辑评论
     * 
     * @param userId 用户ID
     * @param commentId 评论ID
     * @return 是否有权限
     */
    boolean canUserEditComment(Long userId, Long commentId);
}