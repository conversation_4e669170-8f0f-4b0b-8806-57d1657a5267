package edu.sbs.cs.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 电影实体类
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Entity
@Table(name = "movies")
@EntityListeners(AuditingEntityListener.class)
public class Movie {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 255)
    @Column(name = "title", nullable = false)
    private String title;
    
    @Size(max = 255)
    @Column(name = "director")
    private String director;
    
    @Column(name = "actors", columnDefinition = "TEXT")
    private String actors;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "genre")
    private Genre genre;
    
    @Column(name = "release_date")
    private LocalDate releaseDate;
    
    @Column(name = "duration")
    private Integer duration; // 电影时长（分钟）
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "poster_url")
    private String posterUrl;
    
    @Column(name = "trailer_url")
    private String trailerUrl;
    
    @Column(name = "imdb_rating", precision = 3, scale = 1)
    private BigDecimal imdbRating;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 一对多关系：电影的评分
    @OneToMany(mappedBy = "movie", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Rating> ratings = new HashSet<>();
    
    // 一对多关系：电影的评论
    @OneToMany(mappedBy = "movie", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Comment> comments = new HashSet<>();
    
    // 电影类型枚举
    public enum Genre {
        ACTION("动作"),
        COMEDY("喜剧"),
        DRAMA("剧情"),
        HORROR("恐怖"),
        ROMANCE("爱情"),
        THRILLER("惊悚"),
        SCIENCE_FICTION("科幻"),
        FANTASY("奇幻"),
        ANIMATION("动画"),
        DOCUMENTARY("纪录片"),
        CRIME("犯罪"),
        ADVENTURE("冒险"),
        FAMILY("家庭"),
        MYSTERY("悬疑"),
        WAR("战争");
        
        private final String displayName;
        
        Genre(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // 默认构造函数
    public Movie() {}
    
    // 构造函数
    public Movie(String title, String director, Genre genre) {
        this.title = title;
        this.director = director;
        this.genre = genre;
    }
    
    // 计算平均评分 - 注意：此方法不应在JSON序列化时调用，应通过服务层获取
    public Double getAverageRating() {
        // 避免在JSON序列化时触发懒加载，返回null
        // 实际评分应通过MovieService.getAverageRating(movieId)获取
        return null;
    }
    
    // 获取评分数量 - 注意：此方法不应在JSON序列化时调用，应通过服务层获取
    public Integer getRatingCount() {
        // 避免在JSON序列化时触发懒加载，返回null
        // 实际评分数量应通过MovieService.getRatingCount(movieId)获取
        return null;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDirector() {
        return director;
    }
    
    public void setDirector(String director) {
        this.director = director;
    }
    
    public String getActors() {
        return actors;
    }
    
    public void setActors(String actors) {
        this.actors = actors;
    }
    
    public Genre getGenre() {
        return genre;
    }
    
    public void setGenre(Genre genre) {
        this.genre = genre;
    }
    
    public LocalDate getReleaseDate() {
        return releaseDate;
    }
    
    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }
    
    public Integer getDuration() {
        return duration;
    }
    
    public void setDuration(Integer duration) {
        this.duration = duration;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getPosterUrl() {
        return posterUrl;
    }
    
    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }
    
    public String getTrailerUrl() {
        return trailerUrl;
    }
    
    public void setTrailerUrl(String trailerUrl) {
        this.trailerUrl = trailerUrl;
    }
    
    public BigDecimal getImdbRating() {
        return imdbRating;
    }
    
    public void setImdbRating(BigDecimal imdbRating) {
        this.imdbRating = imdbRating;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Set<Rating> getRatings() {
        return ratings;
    }
    
    public void setRatings(Set<Rating> ratings) {
        this.ratings = ratings;
    }
    
    public Set<Comment> getComments() {
        return comments;
    }
    
    public void setComments(Set<Comment> comments) {
        this.comments = comments;
    }
    
    @Override
    public String toString() {
        return "Movie{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", director='" + director + '\'' +
                ", genre=" + genre +
                ", releaseDate=" + releaseDate +
                ", duration=" + duration +
                '}';
    }
}