edu\sbs\cs\service\UserDetailsServiceImpl.class
edu\sbs\cs\dto\JwtResponse.class
edu\sbs\cs\service\RatingService.class
edu\sbs\cs\service\UserService.class
edu\sbs\cs\dto\LoginRequest.class
edu\sbs\cs\entity\Rating.class
edu\sbs\cs\entity\User$Role.class
edu\sbs\cs\config\SwaggerConfig.class
edu\sbs\cs\dto\MovieDTO.class
edu\sbs\cs\config\SecurityConfig.class
edu\sbs\cs\MovieRecommendationApplication.class
edu\sbs\cs\service\MovieService.class
edu\sbs\cs\controller\MovieController.class
edu\sbs\cs\entity\Movie$Genre.class
edu\sbs\cs\config\JwtAuthTokenFilter.class
edu\sbs\cs\service\impl\RatingServiceImpl.class
edu\sbs\cs\controller\RatingController.class
edu\sbs\cs\repository\RatingRepository.class
edu\sbs\cs\util\JwtUtils.class
edu\sbs\cs\config\JwtAuthEntryPoint.class
edu\sbs\cs\repository\UserRepository.class
edu\sbs\cs\service\impl\MovieServiceImpl.class
edu\sbs\cs\controller\AuthController.class
edu\sbs\cs\service\impl\UserServiceImpl.class
edu\sbs\cs\repository\CommentRepository.class
edu\sbs\cs\controller\UserController.class
edu\sbs\cs\dto\RegisterRequest.class
edu\sbs\cs\entity\Comment.class
edu\sbs\cs\entity\User.class
edu\sbs\cs\service\CommentService.class
edu\sbs\cs\service\UserDetailsServiceImpl$UserPrincipal.class
edu\sbs\cs\dto\ApiResponse.class
edu\sbs\cs\controller\CommentController.class
edu\sbs\cs\entity\Movie.class
edu\sbs\cs\exception\GlobalExceptionHandler.class
edu\sbs\cs\repository\MovieRepository.class
edu\sbs\cs\service\impl\CommentServiceImpl.class
