package edu.sbs.cs.controller;

import edu.sbs.cs.dto.ApiResponse;
import edu.sbs.cs.entity.User;
import edu.sbs.cs.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户控制器
 * 处理用户管理相关的API请求
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<User>> getCurrentUserProfile(Authentication authentication) {
        Long userId = Long.parseLong(authentication.getName());
        Optional<User> user = userService.findById(userId);
        
        if (user.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user.get()));
        } else {
            return ResponseEntity.ok(ApiResponse.error("用户不存在", 404));
        }
    }
    
    /**
     * 根据ID获取用户信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户信息")
    public ResponseEntity<ApiResponse<User>> getUserById(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        Optional<User> user = userService.findById(userId);
        
        if (user.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user.get()));
        } else {
            return ResponseEntity.ok(ApiResponse.error("用户不存在", 404));
        }
    }
    
    /**
     * 根据用户名获取用户信息
     */
    @GetMapping("/username/{username}")
    @Operation(summary = "根据用户名获取用户", description = "根据用户名获取用户信息")
    public ResponseEntity<ApiResponse<User>> getUserByUsername(
            @Parameter(description = "用户名", required = true) @PathVariable String username) {
        
        Optional<User> user = userService.findByUsername(username);
        
        if (user.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user.get()));
        } else {
            return ResponseEntity.ok(ApiResponse.error("用户不存在", 404));
        }
    }
    
    /**
     * 获取所有用户列表（管理员）
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取所有用户", description = "分页获取所有用户列表（管理员权限）")
    public ResponseEntity<ApiResponse<Page<User>>> getAllUsers(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size,
            @Parameter(description = "排序字段", example = "createdAt") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向", example = "desc") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<User> users = userService.getAllUsers(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户列表成功", users));
    }
    
    /**
     * 根据角色获取用户列表（管理员）
     */
    @GetMapping("/role/{role}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "根据角色获取用户", description = "根据用户角色分页获取用户列表（管理员权限）")
    public ResponseEntity<ApiResponse<Page<User>>> getUsersByRole(
            @Parameter(description = "用户角色", required = true, example = "USER") @PathVariable String role,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        User.Role userRole = User.Role.valueOf(role.toUpperCase());
        Page<User> users = userService.findByRole(userRole, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取角色用户列表成功", users));
    }
    
    /**
     * 搜索用户
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "搜索用户", description = "根据用户名关键词搜索用户（管理员权限）")
    public ResponseEntity<ApiResponse<Page<User>>> searchUsers(
            @Parameter(description = "搜索关键词", required = true) @RequestParam @NotBlank String keyword,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<User> users = userService.searchUsers(keyword, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("搜索用户成功", users));
    }
    
    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "更新用户资料", description = "用户更新自己的基本资料")
    public ResponseEntity<ApiResponse<User>> updateUserProfile(
            @Parameter(description = "邮箱") @RequestParam(required = false) @Email String email,
            @Parameter(description = "个人简介") @RequestParam(required = false) @Size(max = 500) String bio,
            @Parameter(description = "头像URL") @RequestParam(required = false) @Size(max = 255) String avatarUrl,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        User updatedUser = userService.updateUserProfile(userId, email, bio, avatarUrl);
        
        return ResponseEntity.ok(ApiResponse.success("更新用户资料成功", updatedUser));
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/password")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "修改密码", description = "用户修改自己的密码")
    public ResponseEntity<ApiResponse<String>> changePassword(
            @Parameter(description = "当前密码", required = true) @RequestParam @NotBlank String currentPassword,
            @Parameter(description = "新密码", required = true) @RequestParam @NotBlank @Size(min = 6, max = 20) String newPassword,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        
        // 验证当前密码
        if (!userService.validatePassword(userId, currentPassword)) {
            return ResponseEntity.ok(ApiResponse.error("当前密码错误", 400));
        }
        
        userService.updatePassword(userId, newPassword);
        
        return ResponseEntity.ok(ApiResponse.success("密码修改成功"));
    }
    
    /**
     * 更新头像
     */
    @PutMapping("/avatar")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "更新头像", description = "用户更新头像")
    public ResponseEntity<ApiResponse<User>> updateAvatar(
            @Parameter(description = "头像URL", required = true) @RequestParam @NotBlank @Size(max = 255) String avatarUrl,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        User updatedUser = userService.updateAvatar(userId, avatarUrl);
        
        return ResponseEntity.ok(ApiResponse.success("头像更新成功", updatedUser));
    }
    
    /**
     * 更新个人简介
     */
    @PutMapping("/bio")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "更新个人简介", description = "用户更新个人简介")
    public ResponseEntity<ApiResponse<User>> updateBio(
            @Parameter(description = "个人简介", required = true) @RequestParam @Size(max = 500) String bio,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        User updatedUser = userService.updateBio(userId, bio);
        
        return ResponseEntity.ok(ApiResponse.success("个人简介更新成功", updatedUser));
    }
    
    /**
     * 删除用户账户
     */
    @DeleteMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "删除用户账户", description = "用户删除自己的账户")
    public ResponseEntity<ApiResponse<String>> deleteUserAccount(
            @Parameter(description = "密码确认", required = true) @RequestParam @NotBlank String password,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        
        // 验证密码
        if (!userService.validatePassword(userId, password)) {
            return ResponseEntity.ok(ApiResponse.error("密码错误", 400));
        }
        
        boolean deleted = userService.deleteUser(userId);
        
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success("账户删除成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.error("账户删除失败"));
        }
    }
    
    /**
     * 管理员删除用户
     */
    @DeleteMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "管理员删除用户", description = "管理员删除指定用户（管理员权限）")
    public ResponseEntity<ApiResponse<String>> adminDeleteUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        boolean deleted = userService.deleteUser(userId);
        
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.error("用户不存在或删除失败"));
        }
    }
    
    /**
     * 激活用户
     */
    @PutMapping("/{userId}/activate")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "激活用户", description = "管理员激活用户账户（管理员权限）")
    public ResponseEntity<ApiResponse<User>> activateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        User user = userService.activateUser(userId);
        
        return ResponseEntity.ok(ApiResponse.success("用户激活成功", user));
    }
    
    /**
     * 停用用户
     */
    @PutMapping("/{userId}/deactivate")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "停用用户", description = "管理员停用用户账户（管理员权限）")
    public ResponseEntity<ApiResponse<User>> deactivateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        User user = userService.deactivateUser(userId);
        
        return ResponseEntity.ok(ApiResponse.success("用户停用成功", user));
    }
    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取用户统计", description = "获取系统用户统计信息（管理员权限）")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStats() {
        
        Long totalUsers = userService.getUserCount();
        List<User> recentUsers = userService.getRecentUsers(10);
        
        Map<String, Object> stats = Map.of(
            "totalUsers", totalUsers,
            "recentUsers", recentUsers,
            "recentUserCount", recentUsers.size()
        );
        
        return ResponseEntity.ok(ApiResponse.success("获取用户统计成功", stats));
    }
    
    /**
     * 获取最近注册的用户
     */
    @GetMapping("/recent")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取最近注册用户", description = "获取最近注册的用户列表（管理员权限）")
    public ResponseEntity<ApiResponse<Page<User>>> getRecentUsers(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<User> users = userService.getRecentUsers(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取最近注册用户成功", users));
    }
    
    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已存在")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(
            @Parameter(description = "用户名", required = true) @RequestParam @NotBlank String username) {
        
        boolean exists = userService.existsByUsername(username);
        
        return ResponseEntity.ok(ApiResponse.success("检查用户名完成", exists));
    }
    
    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已存在")
    public ResponseEntity<ApiResponse<Boolean>> checkEmail(
            @Parameter(description = "邮箱", required = true) @RequestParam @NotBlank @Email String email) {
        
        boolean exists = userService.existsByEmail(email);
        
        return ResponseEntity.ok(ApiResponse.success("检查邮箱完成", exists));
    }
}