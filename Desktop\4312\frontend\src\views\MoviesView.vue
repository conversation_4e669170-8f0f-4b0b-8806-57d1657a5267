<template>
  <div class="movies">
    <div class="container">
      <!-- 搜索和筛选区域 -->
      <div class="search-section">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索电影..."
            size="large"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="filters">
          <el-select
            v-model="selectedGenre"
            placeholder="选择类型"
            clearable
            @change="handleFilter"
          >
            <el-option label="全部类型" value="" />
            <el-option
              v-for="genre in genres"
              :key="genre"
              :label="genre"
              :value="genre"
            />
          </el-select>
          
          <el-select
            v-model="sortBy"
            placeholder="排序方式"
            @change="handleSort"
          >
            <el-option label="评分从高到低" value="rating-desc" />
            <el-option label="评分从低到高" value="rating-asc" />
            <el-option label="发布时间从新到旧" value="date-desc" />
            <el-option label="发布时间从旧到新" value="date-asc" />
            <el-option label="片名A-Z" value="title-asc" />
            <el-option label="片名Z-A" value="title-desc" />
          </el-select>
        </div>
      </div>
      
      <!-- 电影网格 -->
      <div class="movies-section">
        <div v-if="loading" class="loading">
          <el-skeleton v-for="i in 8" :key="i" :rows="3" animated class="movie-skeleton" />
        </div>
        
        <div v-else-if="filteredMovies.length > 0" class="movie-grid">
          <div
            v-for="movie in paginatedMovies"
            :key="movie.id"
            class="movie-card"
            @click="goToMovie(movie.id)"
          >
            <div class="movie-poster">
              <img :src="movie.poster_url || '/placeholder-movie.svg'" :alt="movie.title" />
              <div class="movie-overlay">
                <el-icon size="24"><VideoPlay /></el-icon>
              </div>
            </div>
            <div class="movie-info">
              <h3>{{ movie.title }}</h3>
              <p class="movie-genre">{{ movie.genre }}</p>
              <p class="movie-year">{{ movie.release_year }}</p>
              <div class="movie-rating">
                <el-rate
                  v-model="movie.average_rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <el-empty description="没有找到相关电影" />
        </div>
        
        <!-- 分页 -->
        <div v-if="filteredMovies.length > pageSize" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="filteredMovies.length"
            layout="prev, pager, next, jumper, total"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getAllMovies, searchMovies, getMoviesByGenre } from '../api/movie'
import type { Movie } from '../api/types'

const router = useRouter()

const movies = ref<Movie[]>([])
const loading = ref(true)
const searchKeyword = ref('')
const selectedGenre = ref('')
const sortBy = ref('rating-desc')
const currentPage = ref(1)
const pageSize = 12

// 电影类型列表
const genres = ref(['DRAMA', 'CRIME', 'ACTION', 'COMEDY', 'THRILLER', 'ROMANCE', 'HORROR', 'SCI_FI', 'FANTASY', 'ADVENTURE'])

// 筛选后的电影
const filteredMovies = computed(() => {
  let result = [...movies.value]
  
  // 按类型筛选
  if (selectedGenre.value) {
    result = result.filter(movie => movie.genre === selectedGenre.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(movie => 
      movie.title.toLowerCase().includes(keyword) ||
      movie.description.toLowerCase().includes(keyword)
    )
  }
  
  // 排序
  switch (sortBy.value) {
    case 'rating-desc':
      result.sort((a, b) => b.average_rating - a.average_rating)
      break
    case 'rating-asc':
      result.sort((a, b) => a.average_rating - b.average_rating)
      break
    case 'date-desc':
      result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      break
    case 'date-asc':
      result.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      break
    case 'title-asc':
      result.sort((a, b) => a.title.localeCompare(b.title))
      break
    case 'title-desc':
      result.sort((a, b) => b.title.localeCompare(a.title))
      break
  }
  
  return result
})

// 分页后的电影
const paginatedMovies = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredMovies.value.slice(start, end)
})

// 跳转到电影详情页
const goToMovie = (id: number) => {
  router.push(`/movies/${id}`)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 处理筛选
const handleFilter = () => {
  currentPage.value = 1
}

// 处理排序
const handleSort = () => {
  currentPage.value = 1
}

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 加载电影数据
const loadMovies = async () => {
  try {
    loading.value = true
    const moviesResponse = await getAllMovies()
    // 从分页响应中提取电影数组
    movies.value = moviesResponse.content || []
  } catch (error) {
    console.error('Failed to load movies:', error)
    ElMessage.error('加载电影数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadMovies()
})
</script>

<style scoped>
.movies {
  min-height: 100vh;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.search-bar {
  margin-bottom: 20px;
}

.filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.filters .el-select {
  width: 150px;
}

.movies-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.movie-skeleton {
  height: 400px;
}

.movie-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.movie-card {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.movie-poster {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.movie-card:hover .movie-poster img {
  transform: scale(1.05);
}

.movie-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  color: white;
}

.movie-card:hover .movie-overlay {
  opacity: 1;
}

.movie-info {
  padding: 15px;
}

.movie-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.movie-genre {
  font-size: 14px;
  color: #409eff;
  margin: 0 0 5px 0;
  font-weight: 500;
}

.movie-director {
  font-size: 13px;
  color: #606266;
  margin: 0 0 5px 0;
}

.movie-year {
  font-size: 13px;
  color: #909399;
  margin: 0 0 10px 0;
}

.movie-rating {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
  
  .filters .el-select {
    width: 100%;
  }
  
  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>