<script setup lang="ts">
import { RouterView, useRouter, useRoute } from 'vue-router'
import { onMounted, computed } from 'vue'
import { useUserStore } from './stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 检查是否为全屏页面（登录/注册）
const isFullscreenPage = computed(() => {
  return route.name === 'login' || route.name === 'register'
})

// 初始化用户信息
onMounted(() => {
  userStore.restoreUser()
  if (userStore.token) {
    userStore.initUser().catch((error) => {
      console.error('Failed to initialize user:', error)
      // 只有在确实是认证错误时才清除登录状态
      if (error.response?.status === 401) {
        ElMessage.error('登录状态已过期，请重新登录')
        userStore.logout()
        router.push('/login')
      } else {
        // 网络错误等其他错误，不清除登录状态
        console.warn('Network error during user initialization, keeping login state')
      }
    })
  }
})

// 登出
const handleLogout = () => {
  userStore.logout()
  router.push('/login')
  ElMessage.success('已成功登出')
}
</script>

<template>
  <div id="app" :class="{ 'fullscreen': isFullscreenPage }">
    <!-- 导航栏 -->
    <el-header v-if="!isFullscreenPage" class="header">
      <div class="header-content">
        <div class="logo">
          <router-link to="/" class="logo-link">
            <el-icon size="24"><VideoPlay /></el-icon>
            <span class="logo-text">电影推荐系统</span>
          </router-link>
        </div>
        
        <nav class="nav">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/movies" class="nav-link">电影</router-link>
          <template v-if="userStore.isLoggedIn">
            <router-link to="/profile" class="nav-link">个人中心</router-link>
            <router-link v-if="userStore.isAdmin" to="/admin" class="nav-link">管理后台</router-link>
          </template>
        </nav>
        
        <div class="user-actions">
          <template v-if="userStore.isLoggedIn">
            <span class="username">{{ userStore.user?.username }}</span>
            <el-button @click="handleLogout" type="primary" plain>登出</el-button>
          </template>
          <template v-else>
            <router-link to="/login">
              <el-button type="primary" plain>登录</el-button>
            </router-link>
            <router-link to="/register">
              <el-button type="primary">注册</el-button>
            </router-link>
          </template>
        </div>
      </div>
    </el-header>
    
    <!-- 主要内容区域 -->
    <main :class="isFullscreenPage ? 'main-fullscreen' : 'main'">
      <RouterView />
    </main>
    
    <!-- 页脚 -->
    <el-footer v-if="!isFullscreenPage" class="footer">
      <div class="footer-content">
        <p>&copy; 2025 电影推荐系统. All rights reserved.</p>
      </div>
    </el-footer>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#app.fullscreen {
  height: 100vh;
  overflow: hidden;
}

.main-fullscreen {
  width: 100vw;
  height: 100vh;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 60px;
  line-height: 60px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #409eff;
  font-weight: bold;
  font-size: 18px;
}

.logo-text {
  margin-left: 8px;
}

.nav {
  display: flex;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: #606266;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #409eff;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.username {
  color: #606266;
  font-weight: 500;
}

.main {
  flex: 1;
  background: #f5f5f5;
}

.footer {
  background: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 0;
  height: 60px;
  line-height: 60px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  color: #909399;
}
</style>
