package edu.sbs.cs.service;

import edu.sbs.cs.dto.RegisterRequest;
import edu.sbs.cs.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 用户服务接口
 * 定义用户相关的业务逻辑方法
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
public interface UserService {
    
    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册的用户
     */
    User registerUser(RegisterRequest registerRequest);
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户
     */
    Optional<User> findById(Long id);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 更新用户信息
     * @param user 用户
     * @return 更新后的用户
     */
    User updateUser(User user);
    
    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    boolean updatePassword(Long userId, String newPassword);
    
    /**
     * 更新用户头像
     * @param userId 用户ID
     * @param avatarUrl 头像URL
     * @return 更新后的用户
     */
    User updateAvatar(Long userId, String avatarUrl);
    
    /**
     * 更新用户个人简介
     * @param userId 用户ID
     * @param bio 个人简介
     * @return 更新后的用户
     */
    User updateBio(Long userId, String bio);
    
    /**
     * 更新用户资料
     * @param userId 用户ID
     * @param email 邮箱
     * @param bio 个人简介
     * @param avatarUrl 头像URL
     * @return 更新后的用户
     */
    User updateUserProfile(Long userId, String email, String bio, String avatarUrl);
    
    /**
     * 删除用户
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long userId);
    
    /**
     * 获取所有用户（分页）
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<User> getAllUsers(Pageable pageable);
    
    /**
     * 根据角色查找用户
     * @param role 角色
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<User> findByRole(User.Role role, Pageable pageable);
    
    /**
     * 搜索用户
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<User> searchUsers(String keyword, Pageable pageable);
    
    /**
     * 获取用户统计信息
     * @return 用户总数
     */
    long getUserCount();
    
    /**
     * 验证用户密码
     * @param userId 用户ID
     * @param rawPassword 原始密码
     * @return 是否匹配
     */
    boolean validatePassword(Long userId, String rawPassword);
    
    /**
     * 获取最近注册的用户
     * @param limit 限制数量
     * @return 用户列表
     */
    List<User> getRecentUsers(int limit);
    
    /**
     * 获取最近注册的用户（分页）
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<User> getRecentUsers(Pageable pageable);
    
    /**
     * 激活用户
     * @param userId 用户ID
     * @return 激活后的用户
     */
    User activateUser(Long userId);
    
    /**
     * 禁用用户
     * @param userId 用户ID
     * @return 禁用后的用户
     */
    User deactivateUser(Long userId);
    
    /**
     * 验证用户密码
     * @param user 用户
     * @param rawPassword 原始密码
     * @return 是否匹配
     */
    boolean validatePassword(User user, String rawPassword);
}