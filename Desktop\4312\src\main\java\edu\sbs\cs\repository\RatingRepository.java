package edu.sbs.cs.repository;

import edu.sbs.cs.entity.Rating;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 评分数据访问接口
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Repository
public interface RatingRepository extends JpaRepository<Rating, Long> {
    
    /**
     * 根据用户ID和电影ID查找评分
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 评分信息
     */
    Optional<Rating> findByUserIdAndMovieId(Long userId, Long movieId);
    
    /**
     * 根据用户ID查找所有评分
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    Page<Rating> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据电影ID查找所有评分
     * @param movieId 电影ID
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    Page<Rating> findByMovieId(Long movieId, Pageable pageable);
    
    /**
     * 根据用户ID查找所有评分（不分页）
     * @param userId 用户ID
     * @return 评分列表
     */
    List<Rating> findByUserId(Long userId);
    
    /**
     * 根据电影ID查找所有评分（不分页）
     * @param movieId 电影ID
     * @return 评分列表
     */
    List<Rating> findByMovieId(Long movieId);
    
    /**
     * 计算电影的平均评分
     * @param movieId 电影ID
     * @return 平均评分
     */
    @Query("SELECT AVG(r.rating) FROM Rating r WHERE r.movie.id = :movieId")
    Double findAverageRatingByMovieId(@Param("movieId") Long movieId);
    
    /**
     * 统计电影的评分数量
     * @param movieId 电影ID
     * @return 评分数量
     */
    Long countByMovieId(Long movieId);
    
    /**
     * 统计用户的评分数量
     * @param userId 用户ID
     * @return 评分数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 根据评分范围查找评分
     * @param minRating 最低评分
     * @param maxRating 最高评分
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    Page<Rating> findByRatingBetween(BigDecimal minRating, BigDecimal maxRating, Pageable pageable);
    
    /**
     * 查找用户的高评分电影（用于推荐算法）
     * @param userId 用户ID
     * @param minRating 最低评分
     * @return 评分列表
     */
    @Query("SELECT r FROM Rating r WHERE r.user.id = :userId AND r.rating >= :minRating")
    List<Rating> findHighRatingsByUser(@Param("userId") Long userId, @Param("minRating") BigDecimal minRating);
    
    /**
     * 查找与指定用户有相似评分的其他用户
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Query(value = "SELECT r2.user_id FROM ratings r1 " +
           "JOIN ratings r2 ON r1.movie_id = r2.movie_id " +
           "WHERE r1.user_id = :userId AND r2.user_id != :userId " +
           "AND ABS(r1.rating - r2.rating) <= 1.0 " +
           "GROUP BY r2.user_id " +
           "ORDER BY COUNT(*) DESC " +
           "LIMIT :limit", nativeQuery = true)
    List<Long> findSimilarUsers(@Param("userId") Long userId, @Param("limit") int limit);
    
    /**
     * 获取电影评分分布统计
     * @param movieId 电影ID
     * @return 评分分布（评分值，数量）
     */
    @Query("SELECT r.rating, COUNT(r) FROM Rating r WHERE r.movie.id = :movieId GROUP BY r.rating ORDER BY r.rating")
    List<Object[]> findRatingDistributionByMovieId(@Param("movieId") Long movieId);
    
    /**
     * 获取用户评分统计
     * @param userId 用户ID
     * @return 用户评分统计信息
     */
    @Query("SELECT AVG(r.rating), COUNT(r), MIN(r.rating), MAX(r.rating) FROM Rating r WHERE r.user.id = :userId")
    Object[] findUserRatingStatistics(@Param("userId") Long userId);
    
    /**
     * 检查用户是否已对电影评分
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 是否已评分
     */
    Boolean existsByUserIdAndMovieId(Long userId, Long movieId);
    
    /**
     * 删除用户对电影的评分
     * @param userId 用户ID
     * @param movieId 电影ID
     */
    void deleteByUserIdAndMovieId(Long userId, Long movieId);
    
    /**
     * 获取最近的评分记录
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    Page<Rating> findByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 计算用户的平均评分
     * @param userId 用户ID
     * @return 平均评分
     */
    @Query("SELECT AVG(r.rating) FROM Rating r WHERE r.user.id = :userId")
    Double findAverageRatingByUserId(@Param("userId") Long userId);
    
    /**
     * 根据电影ID删除所有评分
     * @param movieId 电影ID
     */
    void deleteByMovieId(Long movieId);
    
    /**
     * 根据用户ID删除所有评分
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
    
    /**
     * 获取全局平均评分
     * @return 全局平均评分
     */
    @Query("SELECT AVG(r.rating) FROM Rating r")
    Double findGlobalAverageRating();
    
    /**
     * 获取评分最高的电影
     * @param pageable 分页信息
     * @return 电影ID列表
     */
    @Query("SELECT r.movie.id FROM Rating r GROUP BY r.movie.id ORDER BY AVG(r.rating) DESC")
    Page<Long> findTopRatedMovies(Pageable pageable);
    
    /**
     * 获取评分最高的电影评分记录
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    @Query("SELECT r FROM Rating r WHERE r.movie.id IN (SELECT r2.movie.id FROM Rating r2 GROUP BY r2.movie.id ORDER BY AVG(r2.rating) DESC)")
    Page<Rating> findTopRatedMovieRatings(Pageable pageable);
    
    /**
     * 根据评分范围查找评分
     * @param minScore 最低评分
     * @param maxScore 最高评分
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    @Query("SELECT r FROM Rating r WHERE r.rating BETWEEN :minScore AND :maxScore")
    Page<Rating> findByScoreBetween(@Param("minScore") Double minScore, @Param("maxScore") Double maxScore, Pageable pageable);
    
    /**
     * 查找用户的高评分电影
     * @param userId 用户ID
     * @param minScore 最低评分
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    @Query("SELECT r FROM Rating r WHERE r.user.id = :userId AND r.rating >= :minScore")
    Page<Rating> findHighRatedMoviesByUser(@Param("userId") Long userId, @Param("minScore") Double minScore, Pageable pageable);
    
    /**
     * 查找相似用户的评分
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    @Query("SELECT r FROM Rating r WHERE r.user.id IN (SELECT r2.user.id FROM Rating r1 JOIN Rating r2 ON r1.movie.id = r2.movie.id WHERE r1.user.id = :userId AND r2.user.id != :userId GROUP BY r2.user.id ORDER BY COUNT(*) DESC)")
    Page<Rating> findSimilarUsers(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 获取最近的评分记录
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    @Query("SELECT r FROM Rating r ORDER BY r.createdAt DESC")
    Page<Rating> findRecentRatings(Pageable pageable);
    
    /**
     * 获取用户最近的评分记录
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 评分分页列表
     */
    @Query("SELECT r FROM Rating r WHERE r.user.id = :userId ORDER BY r.createdAt DESC")
    Page<Rating> findRecentRatingsByUser(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 获取用户最高评分
     * @param userId 用户ID
     * @return 最高评分
     */
    @Query("SELECT MAX(r.rating) FROM Rating r WHERE r.user.id = :userId")
    Double findMaxRatingByUserId(@Param("userId") Long userId);
    
    /**
     * 获取用户最低评分
     * @param userId 用户ID
     * @return 最低评分
     */
    @Query("SELECT MIN(r.rating) FROM Rating r WHERE r.user.id = :userId")
    Double findMinRatingByUserId(@Param("userId") Long userId);
}