package edu.sbs.cs.repository;

import edu.sbs.cs.entity.Movie;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 电影数据访问接口
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Repository
public interface MovieRepository extends JpaRepository<Movie, Long> {
    
    /**
     * 根据标题查找电影
     * @param title 电影标题
     * @return 电影信息
     */
    List<Movie> findByTitleContainingIgnoreCase(String title);
    
    /**
     * 根据导演查找电影
     * @param director 导演姓名
     * @return 电影列表
     */
    List<Movie> findByDirectorContainingIgnoreCase(String director);
    
    /**
     * 根据导演查找电影（分页）
     * @param director 导演姓名
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    Page<Movie> findByDirectorContainingIgnoreCase(String director, Pageable pageable);
    
    /**
     * 根据类型查找电影
     * @param genre 电影类型
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    Page<Movie> findByGenre(Movie.Genre genre, Pageable pageable);
    
    /**
     * 根据发布年份查找电影
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    Page<Movie> findByReleaseDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * 多条件搜索电影
     * @param keyword 关键词（标题、导演、演员）
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m WHERE " +
           "LOWER(m.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.director) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.actors) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Movie> searchMovies(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 多条件搜索电影
     * @param title 电影标题
     * @param director 导演
     * @param genre 电影类型
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m WHERE " +
           "(:title IS NULL OR LOWER(m.title) LIKE LOWER(CONCAT('%', :title, '%'))) AND " +
           "(:director IS NULL OR LOWER(m.director) LIKE LOWER(CONCAT('%', :director, '%'))) AND " +
           "(:genre IS NULL OR m.genre = :genre)")
    Page<Movie> findByMultipleConditions(@Param("title") String title, @Param("director") String director, @Param("genre") Movie.Genre genre, Pageable pageable);
    
    /**
     * 根据平均评分排序获取热门电影
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m LEFT JOIN m.ratings r " +
           "GROUP BY m.id " +
           "ORDER BY AVG(r.rating) DESC, COUNT(r) DESC")
    Page<Movie> findPopularMovies(Pageable pageable);
    
    /**
     * 获取最新电影
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    Page<Movie> findByOrderByReleaseDateDesc(Pageable pageable);
    
    /**
     * 获取高评分电影（平均评分大于指定值）
     * @param minRating 最低评分
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m LEFT JOIN m.ratings r " +
           "GROUP BY m.id " +
           "HAVING AVG(r.rating) >= :minRating " +
           "ORDER BY AVG(r.rating) DESC")
    Page<Movie> findHighRatedMovies(@Param("minRating") Double minRating, Pageable pageable);
    
    /**
     * 根据类型获取推荐电影
     * @param genres 电影类型列表
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m WHERE m.genre IN :genres " +
           "ORDER BY m.createdAt DESC")
    Page<Movie> findByGenreIn(@Param("genres") List<Movie.Genre> genres, Pageable pageable);
    
    /**
     * 获取电影总数
     * @return 电影总数
     */
    @Query("SELECT COUNT(m) FROM Movie m")
    Long countMovies();
    
    /**
     * 根据类型统计电影数量
     * @param genre 电影类型
     * @return 电影数量
     */
    Long countByGenre(Movie.Genre genre);
    
    /**
     * 获取有评分的电影（用于推荐算法）
     * @return 电影列表
     */
    @Query("SELECT DISTINCT m FROM Movie m JOIN m.ratings r")
    List<Movie> findMoviesWithRatings();
    
    /**
     * 获取有评分的电影（分页）
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT DISTINCT m FROM Movie m JOIN m.ratings r")
    Page<Movie> findMoviesWithRatings(Pageable pageable);
    
    /**
     * 根据类型推荐电影
     * @param genre 电影类型
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m WHERE m.genre = :genre ORDER BY m.createdAt DESC")
    Page<Movie> findRecommendedMoviesByGenre(@Param("genre") Movie.Genre genre, Pageable pageable);
    
    /**
     * 根据用户评分查找电影
     * @param userId 用户ID
     * @param pageable 分页信息
     * @return 电影分页列表
     */
    @Query("SELECT m FROM Movie m JOIN m.ratings r WHERE r.user.id = :userId ORDER BY r.rating DESC")
    Page<Movie> findMoviesByUserRatings(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 根据用户评分历史推荐相似电影
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 电影列表
     */
    @Query(value = "SELECT m.* FROM movies m " +
           "WHERE m.id NOT IN (SELECT r.movie_id FROM ratings r WHERE r.user_id = :userId) " +
           "AND m.genre IN (" +
           "  SELECT m2.genre FROM movies m2 " +
           "  JOIN ratings r2 ON m2.id = r2.movie_id " +
           "  WHERE r2.user_id = :userId AND r2.rating >= 7.0" +
           ") " +
           "ORDER BY RAND() LIMIT :limit", nativeQuery = true)
    List<Movie> findRecommendedMoviesForUser(@Param("userId") Long userId, @Param("limit") int limit);
}