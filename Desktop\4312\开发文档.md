# 🎬 电影推荐系统开发文档

## 📋 项目概述

### 项目名称
电影推荐系统 (Movie Recommendation System)

### 项目描述
基于 Spring Boot + Vue3 的前后端分离电影推荐系统，提供电影信息展示、用户评分评论、个性化推荐等功能。

### 技术栈

#### 后端技术栈
- **框架**: Spring Boot 3.3.10
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0 + Spring Data JPA
- **构建工具**: Maven
- **Java版本**: Java 17
- **API文档**: Swagger/OpenAPI 3
- **测试**: JUnit 5

#### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **数据可视化**: ECharts

## 🏗️ 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)    │────│   后端 (Spring) │────│   数据库 (MySQL) │
│                 │    │                 │    │                 │
│ - 用户界面       │    │ - RESTful API   │    │ - 用户数据       │
│ - 路由管理       │    │ - 业务逻辑       │    │ - 电影数据       │
│ - 状态管理       │    │ - 数据访问       │    │ - 评分数据       │
│ - HTTP请求       │    │ - 安全认证       │    │ - 评论数据       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 后端分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Controller Layer                         │
│  AuthController | MovieController | RatingController       │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                     Service Layer                          │
│   UserService | MovieService | RatingService               │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                   Repository Layer                         │
│  UserRepository | MovieRepository | RatingRepository       │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                    Database Layer                          │
│                      MySQL                                 │
└─────────────────────────────────────────────────────────────┘
```

## 📊 数据库设计

### ER图
```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│    User     │       │   Rating    │       │    Movie    │
├─────────────┤       ├─────────────┤       ├─────────────┤
│ id (PK)     │───────│ user_id(FK) │───────│ id (PK)     │
│ username    │       │ movie_id(FK)│       │ title       │
│ email       │       │ rating      │       │ director    │
│ password    │       │ created_at  │       │ release_date│
│ created_at  │       └─────────────┘       │ genre       │
│ updated_at  │                             │ description │
└─────────────┘                             │ poster_url  │
       │                                    │ duration    │
       │         ┌─────────────┐            │ created_at  │
       │         │   Comment   │            │ updated_at  │
       │         ├─────────────┤            └─────────────┘
       └─────────│ user_id(FK) │                    │
                 │ movie_id(FK)│────────────────────┘
                 │ content     │
                 │ likes       │
                 │ created_at  │
                 │ updated_at  │
                 └─────────────┘
```

### 数据表结构

#### 1. users 用户表
```sql
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(255),
    bio TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. movies 电影表
```sql
CREATE TABLE movies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    director VARCHAR(255),
    actors TEXT,
    genre VARCHAR(100),
    release_date DATE,
    duration INT,
    description TEXT,
    poster_url VARCHAR(255),
    trailer_url VARCHAR(255),
    imdb_rating DECIMAL(3,1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. ratings 评分表
```sql
CREATE TABLE ratings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    movie_id BIGINT NOT NULL,
    rating DECIMAL(3,1) NOT NULL CHECK (rating >= 1 AND rating <= 10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_movie (user_id, movie_id)
);
```

#### 4. comments 评论表
```sql
CREATE TABLE comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    movie_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    likes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE
);
```

## 🔌 API设计

### 认证相关 API

#### 用户注册
```
POST /api/auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string",
  "password": "string"
}

Response:
{
  "success": true,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "string",
    "email": "string"
  }
}
```

#### 用户登录
```
POST /api/auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string"
}

Response:
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_string",
    "user": {
      "id": 1,
      "username": "string",
      "email": "string"
    }
  }
}
```

### 电影相关 API

#### 获取电影列表
```
GET /api/movies?page=0&size=20&genre=&sort=rating
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "title": "电影标题",
        "director": "导演",
        "genre": "动作",
        "releaseDate": "2024-01-01",
        "posterUrl": "poster.jpg",
        "averageRating": 8.5,
        "ratingCount": 1000
      }
    ],
    "totalElements": 100,
    "totalPages": 5,
    "currentPage": 0
  }
}
```

#### 获取电影详情
```
GET /api/movies/{id}
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "id": 1,
    "title": "电影标题",
    "director": "导演",
    "actors": "演员1, 演员2",
    "genre": "动作",
    "releaseDate": "2024-01-01",
    "duration": 120,
    "description": "电影描述",
    "posterUrl": "poster.jpg",
    "trailerUrl": "trailer.mp4",
    "averageRating": 8.5,
    "ratingCount": 1000,
    "userRating": 9.0
  }
}
```

#### 搜索电影
```
GET /api/movies/search?q=关键词&page=0&size=20
Authorization: Bearer {token}

Response: 同获取电影列表
```

#### 获取推荐电影
```
GET /api/movies/recommend?type=popular&limit=10
Authorization: Bearer {token}

type: popular(热门), latest(最新), rated(高评分), personal(个性化)

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "电影标题",
      "posterUrl": "poster.jpg",
      "averageRating": 8.5,
      "reason": "推荐理由"
    }
  ]
}
```

### 评分相关 API

#### 添加/更新评分
```
POST /api/ratings
Authorization: Bearer {token}
Content-Type: application/json

{
  "movieId": 1,
  "rating": 8.5
}

Response:
{
  "success": true,
  "message": "评分成功",
  "data": {
    "id": 1,
    "movieId": 1,
    "rating": 8.5,
    "createdAt": "2024-01-01T10:00:00"
  }
}
```

#### 获取用户评分
```
GET /api/ratings/user/{movieId}
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "rating": 8.5,
    "createdAt": "2024-01-01T10:00:00"
  }
}
```

### 评论相关 API

#### 添加评论
```
POST /api/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "movieId": 1,
  "content": "评论内容"
}

Response:
{
  "success": true,
  "message": "评论成功",
  "data": {
    "id": 1,
    "content": "评论内容",
    "likes": 0,
    "user": {
      "username": "用户名",
      "avatarUrl": "avatar.jpg"
    },
    "createdAt": "2024-01-01T10:00:00"
  }
}
```

#### 获取电影评论
```
GET /api/comments/movie/{movieId}?page=0&size=20
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "content": "评论内容",
        "likes": 5,
        "user": {
          "username": "用户名",
          "avatarUrl": "avatar.jpg"
        },
        "createdAt": "2024-01-01T10:00:00"
      }
    ],
    "totalElements": 50,
    "totalPages": 3,
    "currentPage": 0
  }
}
```

## 🎨 前端页面设计

### 页面结构
```
├── 登录页面 (/login)
├── 注册页面 (/register)
├── 首页 (/)
│   ├── 导航栏
│   ├── 轮播图
│   ├── 推荐电影
│   └── 热门电影
├── 电影列表页 (/movies)
│   ├── 搜索框
│   ├── 筛选器
│   └── 电影网格
├── 电影详情页 (/movies/:id)
│   ├── 电影信息
│   ├── 评分组件
│   ├── 评论列表
│   └── 推荐电影
├── 个人中心 (/profile)
│   ├── 个人信息
│   ├── 我的评分
│   └── 我的评论
└── 管理后台 (/admin)
    ├── 电影管理
    └── 用户管理
```

### 组件设计

#### 1. 通用组件
- **Header**: 导航栏组件
- **Footer**: 页脚组件
- **MovieCard**: 电影卡片组件
- **RatingStars**: 星级评分组件
- **Pagination**: 分页组件
- **SearchBox**: 搜索框组件

#### 2. 业务组件
- **MovieList**: 电影列表组件
- **MovieDetail**: 电影详情组件
- **CommentList**: 评论列表组件
- **RecommendMovies**: 推荐电影组件
- **UserProfile**: 用户资料组件

## 🔧 开发计划

### 第一阶段：项目初始化 (1-2天)
1. 创建Spring Boot项目
2. 配置数据库连接
3. 创建基础实体类
4. 配置Spring Security + JWT
5. 创建Vue3项目
6. 配置前端路由和状态管理

### 第二阶段：后端API开发 (3-5天)
1. 实现用户认证API
2. 实现电影管理API
3. 实现评分API
4. 实现评论API
5. 实现推荐算法
6. 编写API文档

### 第三阶段：前端页面开发 (4-6天)
1. 实现登录注册页面
2. 实现首页和电影列表页
3. 实现电影详情页
4. 实现个人中心页面
5. 实现管理后台
6. 优化UI和用户体验

### 第四阶段：功能完善和测试 (2-3天)
1. 添加数据可视化
2. 完善推荐算法
3. 进行功能测试
4. 性能优化
5. 部署准备

### 第五阶段：文档和部署 (1-2天)
1. 完善项目文档
2. 准备演示数据
3. 项目部署
4. 最终测试

## 📝 开发规范

### 后端开发规范
1. **命名规范**：使用驼峰命名法
2. **包结构**：按功能模块划分包
3. **异常处理**：统一异常处理机制
4. **日志记录**：使用SLF4J记录关键操作
5. **代码注释**：重要方法必须添加注释

### 前端开发规范
1. **组件命名**：使用PascalCase
2. **文件命名**：使用kebab-case
3. **代码格式**：使用Prettier格式化
4. **类型定义**：充分利用TypeScript类型系统
5. **状态管理**：合理使用Pinia管理全局状态

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🚀 部署方案

### 开发环境
- 后端：Spring Boot内置Tomcat
- 前端：Vite开发服务器
- 数据库：本地MySQL

### 生产环境
- 后端：打包为JAR文件，使用Java运行
- 前端：构建静态文件，使用Nginx部署
- 数据库：云数据库MySQL

## 📊 测试计划

### 单元测试
- 使用JUnit 5测试Service层业务逻辑
- 使用Mockito模拟依赖
- 测试覆盖率目标：80%以上

### 集成测试
- 使用@SpringBootTest测试API接口
- 使用TestContainers测试数据库操作
- 测试用户认证和权限控制

### 前端测试
- 使用Vitest进行单元测试
- 测试组件渲染和用户交互
- 测试API调用和状态管理

---

**开发文档版本**: v1.0  
**创建时间**: 2025年1月  
**更新时间**: 2025年1月  
**文档状态**: 初始版本