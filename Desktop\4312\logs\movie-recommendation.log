2025-07-26 02:07:46 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2h59m42s661ms915?s500ns).
2025-07-26 07:20:57 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5h13m11s411ms455?s800ns).
2025-07-26 07:52:20 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/auth/me
2025-07-26 07:52:20 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:52:20 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/auth/me
2025-07-26 07:52:20 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/movies
2025-07-26 07:52:20 [http-nio-8080-exec-10] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - No JWT token found in request to: /api/movies
2025-07-26 07:52:20 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:52:20 [http-nio-8080-exec-10] ERROR edu.sbs.cs.config.JwtAuthEntryPoint - Unauthorized error: Full authentication is required to access this resource
2025-07-26 07:52:48 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-07-26 07:52:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-26 07:52:48 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:52:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-26 07:52:48 [http-nio-8080-exec-1] INFO  e.sbs.cs.controller.AuthController - User login attempt: admin123
2025-07-26 07:52:48 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:52:48 [http-nio-8080-exec-1] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Failed to authenticate since password does not match stored value
2025-07-26 07:52:48 [http-nio-8080-exec-1] ERROR e.sbs.cs.controller.AuthController - Login failed for user: admin123, error: Bad credentials
2025-07-26 07:52:56 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-26 07:52:56 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:52:56 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-26 07:52:56 [http-nio-8080-exec-8] INFO  e.sbs.cs.controller.AuthController - User login attempt: admin123
2025-07-26 07:52:56 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:52:56 [http-nio-8080-exec-8] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Failed to authenticate since password does not match stored value
2025-07-26 07:52:56 [http-nio-8080-exec-8] ERROR e.sbs.cs.controller.AuthController - Login failed for user: admin123, error: Bad credentials
2025-07-26 07:53:04 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-26 07:53:04 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:53:04 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-26 07:53:04 [http-nio-8080-exec-4] INFO  e.sbs.cs.controller.AuthController - User login attempt: admin123
2025-07-26 07:53:04 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:53:05 [http-nio-8080-exec-4] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-26 07:53:05 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-26 07:53:05 [http-nio-8080-exec-4] INFO  e.sbs.cs.controller.AuthController - User logged in successfully: admin123
2025-07-26 07:53:05 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/movies
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/movies
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - Set Authentication for user: admin123
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/movies
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - Set Authentication for user: admin123
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.actors,
        m1_0.created_at,
        m1_0.description,
        m1_0.director,
        m1_0.duration,
        m1_0.genre,
        m1_0.imdb_rating,
        m1_0.poster_url,
        m1_0.release_date,
        m1_0.title,
        m1_0.trailer_url,
        m1_0.updated_at 
    from
        movies m1_0 
    order by
        m1_0.created_at desc 
    limit
        ?, ?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(m1_0.id) 
    from
        movies m1_0
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:05 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:53:09 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/auth/me
2025-07-26 07:53:09 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:53:09 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/auth/me
2025-07-26 07:53:09 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/movies
2025-07-26 07:53:09 [http-nio-8080-exec-3] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - No JWT token found in request to: /api/movies
2025-07-26 07:53:09 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:53:09 [http-nio-8080-exec-3] ERROR edu.sbs.cs.config.JwtAuthEntryPoint - Unauthorized error: Full authentication is required to access this resource
2025-07-26 07:54:34 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-26 07:54:34 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:54:34 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-26 07:54:34 [http-nio-8080-exec-9] INFO  e.sbs.cs.controller.AuthController - User login attempt: admin123
2025-07-26 07:54:34 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:54:34 [http-nio-8080-exec-9] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-26 07:54:34 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-26 07:54:34 [http-nio-8080-exec-9] INFO  e.sbs.cs.controller.AuthController - User logged in successfully: admin123
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/movies
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - Set Authentication for user: admin123
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/movies
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - Set Authentication for user: admin123
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.actors,
        m1_0.created_at,
        m1_0.description,
        m1_0.director,
        m1_0.duration,
        m1_0.genre,
        m1_0.imdb_rating,
        m1_0.poster_url,
        m1_0.release_date,
        m1_0.title,
        m1_0.trailer_url,
        m1_0.updated_at 
    from
        movies m1_0 
    order by
        m1_0.created_at desc 
    limit
        ?, ?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(m1_0.id) 
    from
        movies m1_0
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:54:34 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 07:56:15 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/auth/me
2025-07-26 07:56:15 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:56:15 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/auth/me
2025-07-26 07:56:15 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/movies
2025-07-26 07:56:15 [http-nio-8080-exec-4] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - No JWT token found in request to: /api/movies
2025-07-26 07:56:15 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 07:56:15 [http-nio-8080-exec-4] ERROR edu.sbs.cs.config.JwtAuthEntryPoint - Unauthorized error: Full authentication is required to access this resource
2025-07-26 08:36:44 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-26 08:36:44 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-26 08:36:44 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-26 08:36:44 [http-nio-8080-exec-6] INFO  e.sbs.cs.controller.AuthController - User login attempt: admin123
2025-07-26 08:36:44 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 08:36:44 [http-nio-8080-exec-6] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-26 08:36:44 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-26 08:36:44 [http-nio-8080-exec-6] INFO  e.sbs.cs.controller.AuthController - User logged in successfully: admin123
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/movies
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - Set Authentication for user: admin123
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/movies
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.avatar_url,
        u1_0.bio,
        u1_0.created_at,
        u1_0.email,
        u1_0.password,
        u1_0.role,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG e.sbs.cs.config.JwtAuthTokenFilter - Set Authentication for user: admin123
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.actors,
        m1_0.created_at,
        m1_0.description,
        m1_0.director,
        m1_0.duration,
        m1_0.genre,
        m1_0.imdb_rating,
        m1_0.poster_url,
        m1_0.release_date,
        m1_0.title,
        m1_0.trailer_url,
        m1_0.updated_at 
    from
        movies m1_0 
    order by
        m1_0.created_at desc 
    limit
        ?, ?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(m1_0.id) 
    from
        movies m1_0
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(r1_0.rating) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
2025-07-26 08:36:44 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(r1_0.id) 
    from
        ratings r1_0 
    where
        r1_0.movie_id=?
