import api from './index'
import type { Rating, Comment, RatingCreateRequest, RatingUpdateRequest, ApiResponse, PageResponse } from './types'

// 创建评分
export const createRating = (data: RatingCreateRequest) => {
  return api.post('/ratings', data)
}

// 获取用户对电影的评分
export const getUserRating = (movieId: number) => {
  return api.get(`/ratings/movie/${movieId}/user`)
}

// 获取电影的所有评分
export const getMovieRatings = (movieId: number) => {
  return api.get(`/ratings/movie/${movieId}`)
}

// 获取电影的评分和评论（分页）
export const getRatingsByMovie = (movieId: number, page: number = 1, size: number = 10) => {
  return api.get(`/ratings/movie/${movieId}?page=${page}&size=${size}`)
}

// 更新评分
export const updateRating = (ratingId: number, rating: number) => {
  return api.put(`/ratings/${ratingId}`, { rating })
}

// 删除评分
export const deleteRating = (ratingId: number) => {
  return api.delete(`/ratings/${ratingId}`)
}

// 创建评论
export const createComment = (movieId: number, content: string) => {
  return api.post('/comments', { movieId, content })
}

// 获取电影的所有评论
export const getMovieComments = (movieId: number) => {
  return api.get(`/comments/movie/${movieId}`)
}

// 更新评论
export const updateComment = (commentId: number, content: string) => {
  return api.put(`/comments/${commentId}`, { content })
}

// 删除评论
export const deleteComment = (commentId: number) => {
  return api.delete(`/comments/${commentId}`)
}