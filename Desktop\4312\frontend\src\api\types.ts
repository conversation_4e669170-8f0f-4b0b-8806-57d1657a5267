// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  avatar_url?: string
  bio?: string
  role: 'USER' | 'ADMIN'
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}

// 电影相关类型
export interface Movie {
  id: number
  title: string
  description: string
  genre: string
  release_year: number
  duration: number
  poster_url?: string
  average_rating: number
  rating_count: number
  created_at: string
  updated_at: string
}

export interface MovieCreateRequest {
  title: string
  description: string
  genre: string
  release_year: number
  duration: number
  poster_url?: string
}

export interface MovieUpdateRequest extends Partial<MovieCreateRequest> {
  id: number
}

// 评分和评论相关类型
export interface Rating {
  id: number
  user_id: number
  movie_id: number
  rating: number
  comment?: string
  created_at: string
  updated_at: string
}

export interface Comment {
  id: number
  user_id: number
  movie_id: number
  username: string
  rating: number
  comment: string
  created_at: string
  updated_at: string
}

export interface RatingCreateRequest {
  movie_id: number
  rating: number
  comment?: string
}

export interface RatingUpdateRequest {
  id: number
  rating?: number
  comment?: string
}

// API响应类型
export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PageResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

// 搜索和过滤参数
export interface MovieSearchParams {
  keyword?: string
  genre?: string
  sortBy?: 'title' | 'release_year' | 'average_rating' | 'created_at'
  sortDirection?: 'asc' | 'desc'
  page?: number
  size?: number
}

export interface RecommendationParams {
  userId?: number
  limit?: number
  genre?: string
}