<template>
  <div class="profile-view">
    <el-card class="profile-card">
      <template #header>
        <div class="profile-header">
          <h2>个人资料</h2>
          <el-button type="primary" @click="editMode = !editMode">
            {{ editMode ? '取消编辑' : '编辑资料' }}
          </el-button>
        </div>
      </template>

      <div v-if="!editMode" class="profile-display">
        <div class="avatar-section">
          <el-avatar
            :size="120"
            :src="userStore.user?.avatar_url"
            :icon="UserFilled"
          />
        </div>
        
        <div class="info-section">
          <div class="info-item">
            <label>用户名：</label>
            <span>{{ userStore.user?.username }}</span>
          </div>
          <div class="info-item">
            <label>邮箱：</label>
            <span>{{ userStore.user?.email }}</span>
          </div>
          <div class="info-item">
            <label>个人简介：</label>
            <span>{{ userStore.user?.bio || '暂无简介' }}</span>
          </div>
          <div class="info-item">
            <label>角色：</label>
            <el-tag :type="userStore.user?.role === 'ADMIN' ? 'danger' : 'primary'">
              {{ userStore.user?.role === 'ADMIN' ? '管理员' : '普通用户' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>注册时间：</label>
            <span>{{ formatDate(userStore.user?.created_at) }}</span>
          </div>
        </div>
      </div>

      <div v-else class="profile-edit">
        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          label-width="100px"
        >
          <el-form-item label="头像">
            <div class="avatar-upload">
              <el-avatar
                :size="120"
                :src="profileForm.avatar_url"
                :icon="UserFilled"
              />
              <el-input
                v-model="profileForm.avatar_url"
                placeholder="请输入头像URL"
                style="margin-top: 10px;"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="profileForm.email" />
          </el-form-item>
          
          <el-form-item label="个人简介">
            <el-input
              v-model="profileForm.bio"
              type="textarea"
              :rows="3"
              placeholder="请输入个人简介"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="updateProfile" :loading="updating">
              保存修改
            </el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 修改密码卡片 -->
    <el-card class="password-card">
      <template #header>
        <h3>修改密码</h3>
      </template>
      
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
            placeholder="请输入当前密码"
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="changePassword" :loading="changingPassword">
            修改密码
          </el-button>
          <el-button @click="resetPasswordForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 我的评分记录 -->
    <el-card class="ratings-card">
      <template #header>
        <h3>我的评分记录</h3>
      </template>
      
      <div v-if="myRatings.length === 0" class="no-ratings">
        <el-empty description="暂无评分记录" />
      </div>
      
      <div v-else class="ratings-list">
        <div
          v-for="rating in myRatings"
          :key="rating.id"
          class="rating-item"
        >
          <div class="movie-info">
            <h4>{{ rating.movie_title }}</h4>
            <el-rate v-model="rating.rating" disabled />
          </div>
          <div class="rating-comment" v-if="rating.comment">
            <p>{{ rating.comment }}</p>
          </div>
          <div class="rating-date">
            <span>{{ formatDate(rating.created_at) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <el-pagination
        v-if="totalRatings > pageSize"
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalRatings"
        layout="prev, pager, next"
        @current-change="loadMyRatings"
        class="pagination"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { updateProfile, changePassword } from '@/api/auth'
import type { FormInstance, FormRules } from 'element-plus'

const userStore = useUserStore()

const editMode = ref(false)
const updating = ref(false)
const changingPassword = ref(false)

const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 个人资料表单
const profileForm = reactive({
  email: '',
  avatar_url: '',
  bio: ''
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 我的评分数据
const myRatings = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalRatings = ref(0)

// 表单验证规则
const profileRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initProfileForm = () => {
  if (userStore.user) {
    profileForm.email = userStore.user.email
    profileForm.avatar_url = userStore.user.avatar_url || ''
    profileForm.bio = userStore.user.bio || ''
  }
}

// 更新个人资料
const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    updating.value = true
    
    await updateProfile(profileForm)
    
    // 更新本地用户信息
    if (userStore.user) {
      userStore.user.email = profileForm.email
      userStore.user.avatar_url = profileForm.avatar_url
      userStore.user.bio = profileForm.bio
    }
    
    ElMessage.success('个人资料更新成功')
    editMode.value = false
  } catch (error) {
    console.error('更新个人资料失败:', error)
    ElMessage.error('更新个人资料失败')
  } finally {
    updating.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    await changePassword(passwordForm.currentPassword, passwordForm.newPassword)
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败')
  } finally {
    changingPassword.value = false
  }
}

// 重置个人资料表单
const resetForm = () => {
  initProfileForm()
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.currentPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 加载我的评分记录
const loadMyRatings = async () => {
  try {
    // 这里应该调用获取用户评分记录的API
    // const response = await getUserRatings(currentPage.value, pageSize.value)
    // myRatings.value = response.data.content
    // totalRatings.value = response.data.totalElements
    
    // 临时模拟数据
    myRatings.value = []
    totalRatings.value = 0
  } catch (error) {
    console.error('加载评分记录失败:', error)
  }
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  initProfileForm()
  loadMyRatings()
})
</script>

<style scoped>
.profile-view {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-card,
.password-card,
.ratings-card {
  margin-bottom: 20px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-header h2 {
  margin: 0;
  color: #333;
}

.profile-display {
  display: flex;
  gap: 30px;
}

.avatar-section {
  flex-shrink: 0;
  text-align: center;
}

.info-section {
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  width: 100px;
  color: #666;
}

.info-item span {
  color: #333;
}

.avatar-upload {
  text-align: center;
}

.no-ratings {
  text-align: center;
  padding: 40px;
}

.ratings-list {
  margin-bottom: 20px;
}

.rating-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 15px 0;
}

.rating-item:last-child {
  border-bottom: none;
}

.movie-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.movie-info h4 {
  margin: 0;
  color: #333;
}

.rating-comment {
  margin-bottom: 8px;
}

.rating-comment p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

.rating-date {
  text-align: right;
}

.rating-date span {
  color: #999;
  font-size: 12px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .profile-display {
    flex-direction: column;
    text-align: center;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item label {
    width: auto;
    margin-bottom: 5px;
  }
  
  .movie-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>