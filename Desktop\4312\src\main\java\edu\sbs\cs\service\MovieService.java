package edu.sbs.cs.service;

import edu.sbs.cs.dto.MovieDTO;
import edu.sbs.cs.entity.Movie;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 电影服务接口
 * 定义电影相关的业务逻辑方法
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
public interface MovieService {
    
    /**
     * 创建电影
     * @param movie 电影信息
     * @return 创建的电影
     */
    Movie createMovie(Movie movie);
    
    /**
     * 更新电影信息
     * @param movie 电影信息
     * @return 更新后的电影
     */
    Movie updateMovie(Movie movie);
    
    /**
     * 根据ID查找电影
     * @param id 电影ID
     * @return 电影
     */
    Optional<Movie> findById(Long id);
    
    /**
     * 根据标题查找电影
     * @param title 电影标题
     * @return 电影列表
     */
    List<Movie> findByTitle(String title);
    
    /**
     * 根据导演查找电影
     * @param director 导演
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> findByDirector(String director, Pageable pageable);
    
    /**
     * 根据类型查找电影
     * @param genre 电影类型
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> findByGenre(Movie.Genre genre, Pageable pageable);
    
    /**
     * 根据发布日期查找电影
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> findByReleaseDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * 多条件搜索电影
     * @param title 标题（可选）
     * @param director 导演（可选）
     * @param genre 类型（可选）
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> searchMovies(String title, String director, Movie.Genre genre, Pageable pageable);
    
    /**
     * 获取热门电影
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> getPopularMovies(Pageable pageable);
    
    /**
     * 获取最新电影
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> getLatestMovies(Pageable pageable);
    
    /**
     * 获取高评分电影
     * @param minRating 最低评分
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> getHighRatedMovies(Double minRating, Pageable pageable);
    
    /**
     * 根据类型获取推荐电影
     * @param genre 电影类型
     * @param limit 限制数量
     * @return 电影列表
     */
    List<Movie> getRecommendedMoviesByGenre(Movie.Genre genre, int limit);
    
    /**
     * 为用户推荐电影
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 电影列表
     */
    List<Movie> getRecommendedMoviesForUser(Long userId, int limit);
    
    /**
     * 获取所有电影（分页）
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> getAllMovies(Pageable pageable);
    
    /**
     * 删除电影
     * @param id 电影ID
     */
    void deleteMovie(Long id);
    
    /**
     * 获取电影统计信息
     * @return 电影总数
     */
    long getMovieCount();
    
    /**
     * 根据类型获取电影数量
     * @param genre 电影类型
     * @return 电影数量
     */
    long getMovieCountByGenre(Movie.Genre genre);
    
    /**
     * 获取带评分的电影
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> getMoviesWithRatings(Pageable pageable);
    
    /**
     * 获取电影的平均评分
     * @param movieId 电影ID
     * @return 平均评分
     */
    Double getAverageRating(Long movieId);
    
    /**
     * 获取电影的评分数量
     * @param movieId 电影ID
     * @return 评分数量
     */
    Long getRatingCount(Long movieId);
    
    /**
     * 检查电影是否存在
     * @param id 电影ID
     * @return 是否存在
     */
    boolean existsById(Long id);
    
    /**
     * 批量创建电影
     * @param movies 电影列表
     * @return 创建的电影列表
     */
    List<Movie> createMovies(List<Movie> movies);
    
    /**
     * 获取相似电影
     * @param movieId 电影ID
     * @param limit 限制数量
     * @return 相似电影列表
     */
    List<Movie> getSimilarMovies(Long movieId, int limit);
    
    /**
     * 获取用户观看历史
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 电影分页列表
     */
    Page<Movie> getUserWatchHistory(Long userId, Pageable pageable);
    
    /**
     * 将Movie实体转换为MovieDTO
     * @param movie 电影实体
     * @return 电影DTO
     */
    MovieDTO convertToDTO(Movie movie);
    
    /**
     * 获取所有电影DTO（分页）
     * @param pageable 分页参数
     * @return 电影DTO分页列表
     */
    Page<MovieDTO> getAllMoviesDTO(Pageable pageable);
    
    /**
     * 多条件搜索电影DTO
     * @param title 标题（可选）
     * @param director 导演（可选）
     * @param genre 类型（可选）
     * @param pageable 分页参数
     * @return 电影DTO分页列表
     */
    Page<MovieDTO> searchMoviesDTO(String title, String director, Movie.Genre genre, Pageable pageable);
    
    /**
     * 获取热门电影DTO
     * @param pageable 分页参数
     * @return 电影DTO分页列表
     */
    Page<MovieDTO> getPopularMoviesDTO(Pageable pageable);
    
    /**
     * 获取最新电影DTO
     * @param pageable 分页参数
     * @return 电影DTO分页列表
     */
    Page<MovieDTO> getLatestMoviesDTO(Pageable pageable);
    
    /**
     * 获取高评分电影DTO
     * @param minRating 最低评分
     * @param pageable 分页参数
     * @return 电影DTO分页列表
     */
    Page<MovieDTO> getHighRatedMoviesDTO(Double minRating, Pageable pageable);
    
    /**
     * 为用户推荐电影DTO
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 电影DTO列表
     */
    List<MovieDTO> getRecommendedMoviesDTOForUser(Long userId, int limit);
    
    /**
     * 获取相似电影DTO
     * @param movieId 电影ID
     * @param limit 限制数量
     * @return 相似电影DTO列表
     */
    List<MovieDTO> getSimilarMoviesDTO(Long movieId, int limit);
    
    /**
     * 根据类型查找电影DTO
     * @param genre 电影类型
     * @param pageable 分页参数
     * @return 电影DTO分页列表
     */
    Page<MovieDTO> findByGenreDTO(Movie.Genre genre, Pageable pageable);
}