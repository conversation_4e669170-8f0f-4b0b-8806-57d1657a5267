<template>
  <div class="movie-detail" v-loading="loading">
    <div v-if="movie" class="movie-content">
      <!-- 电影信息卡片 -->
      <el-card class="movie-info-card">
        <div class="movie-header">
          <div class="movie-poster">
            <el-image
              :src="movie.poster_url || '/placeholder-movie.svg'"
              :alt="movie.title"
              fit="cover"
              class="poster-image"
            />
          </div>
          <div class="movie-details">
            <h1 class="movie-title">{{ movie.title }}</h1>
            <div class="movie-meta">
              <el-tag type="primary" class="genre-tag">{{ movie.genre }}</el-tag>
              <span class="release-year">{{ movie.release_year }}</span>
              <span class="duration">{{ movie.duration }} 分钟</span>
            </div>
            <div class="rating-section">
              <el-rate
                v-model="movie.average_rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value} 分"
              />
              <span class="rating-count">({{ movie.rating_count || 0 }} 人评价)</span>
            </div>
            <p class="movie-description">{{ movie.description }}</p>
            
            <!-- 用户评分 -->
            <div v-if="userStore.isLoggedIn" class="user-rating">
              <h3>我的评分</h3>
              <el-rate
                v-model="userRating.rating"
                @change="submitRating"
                show-text
                :texts="['极差', '较差', '还行', '推荐', '力荐']"
              />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 评论区 -->
      <el-card class="comments-section">
        <template #header>
          <div class="comments-header">
            <h2>用户评论</h2>
            <el-button
              v-if="userStore.isLoggedIn"
              type="primary"
              @click="showCommentDialog = true"
            >
              写评论
            </el-button>
          </div>
        </template>
        
        <div v-if="comments.length === 0" class="no-comments">
          <el-empty description="暂无评论" />
        </div>
        
        <div v-else class="comments-list">
          <div
            v-for="comment in comments"
            :key="comment.id"
            class="comment-item"
          >
            <div class="comment-header">
              <span class="username">{{ comment.username }}</span>
              <el-rate
                v-model="comment.rating"
                disabled
                size="small"
              />
              <span class="comment-date">{{ formatDate(comment.created_at) }}</span>
            </div>
            <p class="comment-content">{{ comment.comment }}</p>
          </div>
        </div>
        
        <!-- 分页 -->
        <el-pagination
          v-if="totalComments > pageSize"
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalComments"
          layout="prev, pager, next"
          @current-change="loadComments"
          class="pagination"
        />
      </el-card>
    </div>

    <!-- 评论对话框 -->
    <el-dialog
      v-model="showCommentDialog"
      title="写评论"
      width="500px"
    >
      <el-form :model="commentForm" label-width="80px">
        <el-form-item label="评分">
          <el-rate
            v-model="commentForm.rating"
            show-text
            :texts="['极差', '较差', '还行', '推荐', '力荐']"
          />
        </el-form-item>
        <el-form-item label="评论">
          <el-input
            v-model="commentForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入您的评论..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCommentDialog = false">取消</el-button>
        <el-button type="primary" @click="submitComment">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getMovieById } from '@/api/movie'
import { getRatingsByMovie, createRating, updateRating } from '@/api/rating'
import type { Movie, Rating, Comment } from '@/api/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const movie = ref<Movie | null>(null)
const comments = ref<Comment[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalComments = ref(0)

const userRating = ref({ rating: 0 })
const showCommentDialog = ref(false)
const commentForm = ref({
  rating: 0,
  comment: ''
})

// 加载电影详情
const loadMovieDetail = async () => {
  try {
    loading.value = true
    const movieId = Number(route.params.id)
    const response = await getMovieById(movieId)
    movie.value = response.data
  } catch (error) {
    console.error('加载电影详情失败:', error)
    ElMessage.error('加载电影详情失败')
    router.push('/movies')
  } finally {
    loading.value = false
  }
}

// 加载评论
const loadComments = async () => {
  try {
    const movieId = Number(route.params.id)
    const response = await getRatingsByMovie(movieId, currentPage.value, pageSize.value)
    comments.value = response.data.content
    totalComments.value = response.data.totalElements
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

// 提交评分
const submitRating = async (rating: number) => {
  try {
    const movieId = Number(route.params.id)
    await createRating({
      movie_id: movieId,
      rating: rating,
      comment: ''
    })
    ElMessage.success('评分成功')
    loadMovieDetail() // 重新加载电影信息以更新平均评分
  } catch (error) {
    console.error('评分失败:', error)
    ElMessage.error('评分失败')
  }
}

// 提交评论
const submitComment = async () => {
  if (!commentForm.value.rating || !commentForm.value.comment.trim()) {
    ElMessage.warning('请填写评分和评论内容')
    return
  }

  try {
    const movieId = Number(route.params.id)
    await createRating({
      movie_id: movieId,
      rating: commentForm.value.rating,
      comment: commentForm.value.comment
    })
    ElMessage.success('评论提交成功')
    showCommentDialog.value = false
    commentForm.value = { rating: 0, comment: '' }
    loadComments()
    loadMovieDetail()
  } catch (error) {
    console.error('提交评论失败:', error)
    ElMessage.error('提交评论失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadMovieDetail()
  loadComments()
})
</script>

<style scoped>
.movie-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.movie-info-card {
  margin-bottom: 20px;
}

.movie-header {
  display: flex;
  gap: 20px;
}

.movie-poster {
  flex-shrink: 0;
}

.poster-image {
  width: 300px;
  height: 400px;
  border-radius: 8px;
}

.movie-details {
  flex: 1;
}

.movie-title {
  font-size: 2.5em;
  margin-bottom: 10px;
  color: #333;
}

.movie-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.genre-tag {
  font-size: 14px;
}

.release-year,
.duration {
  color: #666;
  font-size: 16px;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.rating-count {
  color: #666;
  font-size: 14px;
}

.movie-description {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 20px;
}

.user-rating {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.user-rating h3 {
  margin-bottom: 10px;
  color: #333;
}

.comments-section {
  margin-top: 20px;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comments-header h2 {
  margin: 0;
  color: #333;
}

.no-comments {
  text-align: center;
  padding: 40px;
}

.comments-list {
  margin-bottom: 20px;
}

.comment-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 15px 0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.username {
  font-weight: bold;
  color: #333;
}

.comment-date {
  color: #999;
  font-size: 12px;
  margin-left: auto;
}

.comment-content {
  color: #555;
  line-height: 1.5;
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .movie-header {
    flex-direction: column;
  }
  
  .poster-image {
    width: 100%;
    max-width: 300px;
    height: auto;
  }
  
  .movie-title {
    font-size: 2em;
  }
  
  .movie-meta {
    flex-wrap: wrap;
  }
}
</style>