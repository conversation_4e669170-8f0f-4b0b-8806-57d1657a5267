package edu.sbs.cs.repository;

import edu.sbs.cs.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户数据访问接口
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户信息
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据用户名或邮箱查找用户
     * @param username 用户名
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByUsernameOrEmail(String username, String email);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    Boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    Boolean existsByEmail(String email);
    
    /**
     * 根据角色查找用户
     * @param role 用户角色
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.role = :role")
    java.util.List<User> findByRole(@Param("role") User.Role role);
    
    /**
     * 根据角色分页查找用户
     * @param role 用户角色
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    @Query("SELECT u FROM User u WHERE u.role = :role")
    Page<User> findByRole(@Param("role") User.Role role, Pageable pageable);
    
    /**
     * 获取用户总数
     * @return 用户总数
     */
    @Query("SELECT COUNT(u) FROM User u")
    Long countUsers();
    
    /**
     * 根据用户名模糊查询
     * @param username 用户名关键词
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.username LIKE %:username%")
    java.util.List<User> findByUsernameContaining(@Param("username") String username);
    
    /**
     * 根据用户名模糊查询（分页）
     * @param username 用户名关键词
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    @Query("SELECT u FROM User u WHERE u.username LIKE %:username%")
    Page<User> findByUsernameContainingIgnoreCase(@Param("username") String username, Pageable pageable);
    
    /**
      * 获取最近注册的用户
      * @param pageable 分页参数
      * @return 用户分页列表
      */
     @Query("SELECT u FROM User u ORDER BY u.createdAt DESC")
     Page<User> findRecentUsers(Pageable pageable);
}