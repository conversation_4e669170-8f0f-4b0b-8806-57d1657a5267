# 电影推荐系统 MySQL 数据库配置说明

## 前置要求

1. **安装 MySQL 8.0+**
   - 下载地址：https://dev.mysql.com/downloads/mysql/
   - 或使用包管理器安装（如 Homebrew、apt、yum 等）

2. **启动 MySQL 服务**
   ```bash
   # Windows
   net start mysql
   
   # macOS (使用 Homebrew)
   brew services start mysql
   
   # Linux (使用 systemctl)
   sudo systemctl start mysql
   ```

## 数据库配置步骤

### 1. 创建数据库用户（可选）

```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建专用用户（推荐）
CREATE USER 'movie_user'@'localhost' IDENTIFIED BY 'movie_password';
GRANT ALL PRIVILEGES ON movie_recommendation.* TO 'movie_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 修改应用配置

如果创建了专用用户，请修改 `src/main/resources/application.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    url: ***************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: movie_user  # 修改为你的用户名
    password: movie_password  # 修改为你的密码
```

### 3. 运行应用

```bash
# 清理并编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run
```

## 数据库结构说明

### 表结构

1. **users** - 用户表
   - 存储用户基本信息、角色等
   - 支持 USER 和 ADMIN 两种角色

2. **movies** - 电影表
   - 存储电影详细信息
   - 支持多种电影类型（动作、喜剧、剧情等）

3. **ratings** - 评分表
   - 存储用户对电影的评分
   - 每个用户对每部电影只能评分一次

4. **comments** - 评论表
   - 存储用户对电影的评论
   - 支持点赞功能

### 视图

1. **movie_stats** - 电影统计视图
   - 包含电影的平均评分、评分数量、评论数量等统计信息

2. **user_activity** - 用户活跃度视图
   - 包含用户的评分数量、评论数量、获得点赞数等信息

### 存储过程

1. **GetMovieRecommendations** - 电影推荐存储过程
   - 基于用户评分历史推荐相似类型的电影

## 示例数据

数据库初始化脚本包含了丰富的示例数据：

- **7个示例用户**：包括管理员和不同偏好的普通用户
- **20部经典电影**：涵盖各种类型的知名电影
- **丰富的评分数据**：展示不同用户的评分偏好
- **详细的评论数据**：包含有意义的电影评论

## 常见问题

### 1. 连接失败

**错误信息**：`Communications link failure`

**解决方案**：
- 确保 MySQL 服务正在运行
- 检查端口 3306 是否被占用
- 验证用户名和密码是否正确

### 2. 字符编码问题

**错误信息**：中文字符显示为乱码

**解决方案**：
- 确保数据库字符集为 `utf8mb4`
- 检查连接字符串中的 `characterEncoding=utf8mb4` 参数

### 3. 时区问题

**错误信息**：`The server time zone value 'XXX' is unrecognized`

**解决方案**：
- 在连接字符串中添加 `serverTimezone=UTC` 参数
- 或设置 MySQL 的时区：`SET GLOBAL time_zone = '+8:00';`

### 4. 权限问题

**错误信息**：`Access denied for user`

**解决方案**：
- 检查用户是否存在：`SELECT User, Host FROM mysql.user;`
- 检查用户权限：`SHOW GRANTS FOR 'username'@'localhost';`
- 重新授权：`GRANT ALL PRIVILEGES ON movie_recommendation.* TO 'username'@'localhost';`

## 性能优化建议

1. **索引优化**
   - 数据库脚本已包含必要的索引
   - 根据查询模式添加复合索引

2. **连接池配置**
   ```yaml
   spring:
     datasource:
       hikari:
         maximum-pool-size: 20
         minimum-idle: 5
         connection-timeout: 30000
         idle-timeout: 600000
         max-lifetime: 1800000
   ```

3. **查询优化**
   - 使用 `movie_stats` 视图获取电影统计信息
   - 利用存储过程进行复杂的推荐算法

## 备份与恢复

### 备份数据库

```bash
mysqldump -u root -p movie_recommendation > movie_recommendation_backup.sql
```

### 恢复数据库

```bash
mysql -u root -p movie_recommendation < movie_recommendation_backup.sql
```

## 监控与维护

1. **查看数据库状态**
   ```sql
   SHOW STATUS LIKE 'Threads_connected';
   SHOW PROCESSLIST;
   ```

2. **查看表大小**
   ```sql
   SELECT 
       table_name AS 'Table',
       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
   FROM information_schema.tables 
   WHERE table_schema = 'movie_recommendation'
   ORDER BY (data_length + index_length) DESC;
   ```

3. **定期维护**
   ```sql
   -- 优化表
   OPTIMIZE TABLE users, movies, ratings, comments;
   
   -- 分析表
   ANALYZE TABLE users, movies, ratings, comments;
   ```