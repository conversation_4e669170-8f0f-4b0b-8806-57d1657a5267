package edu.sbs.cs.service.impl;

import edu.sbs.cs.dto.RegisterRequest;
import edu.sbs.cs.entity.User;
import edu.sbs.cs.repository.UserRepository;
import edu.sbs.cs.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public User registerUser(RegisterRequest registerRequest) {
        logger.info("Registering new user: {}", registerRequest.getUsername());
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerRequest.getUsername())) {
            throw new RuntimeException("用户名已存在: " + registerRequest.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + registerRequest.getEmail());
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setEmail(registerRequest.getEmail());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        user.setBio(registerRequest.getBio());
        user.setAvatarUrl(registerRequest.getAvatarUrl());
        user.setRole(User.Role.USER);
        
        User savedUser = userRepository.save(user);
        logger.info("User registered successfully: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
    
    @Override
    public User updateUser(User user) {
        logger.info("Updating user: {}", user.getUsername());
        return userRepository.save(user);
    }
    
    @Override
    public boolean updatePassword(Long userId, String newPassword) {
        logger.info("Updating password for user ID: {}", userId);
        
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOpt.get();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        logger.info("Password updated successfully for user: {}", user.getUsername());
        return true;
    }
    
    public boolean updatePassword(Long userId, String oldPassword, String newPassword) {
        logger.info("Updating password for user ID: {}", userId);
        
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("旧密码不正确");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        logger.info("Password updated successfully for user: {}", user.getUsername());
        return true;
    }
    
    @Override
    public User updateAvatar(Long userId, String avatarUrl) {
        logger.info("Updating avatar for user ID: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        user.setAvatarUrl(avatarUrl);
        return userRepository.save(user);
    }
    
    @Override
    public User updateBio(Long userId, String bio) {
        logger.info("Updating bio for user ID: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        user.setBio(bio);
        return userRepository.save(user);
    }
    
    @Override
    public User updateUserProfile(Long userId, String email, String bio, String avatarUrl) {
        logger.info("Updating profile for user ID: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        if (email != null && !email.trim().isEmpty()) {
            user.setEmail(email);
        }
        if (bio != null) {
            user.setBio(bio);
        }
        if (avatarUrl != null && !avatarUrl.trim().isEmpty()) {
            user.setAvatarUrl(avatarUrl);
        }
        
        return userRepository.save(user);
    }
    
    @Override
    public boolean deleteUser(Long userId) {
        logger.info("Deleting user ID: {}", userId);
        
        if (!userRepository.existsById(userId)) {
            return false;
        }
        
        userRepository.deleteById(userId);
        logger.info("User deleted successfully: {}", userId);
        return true;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<User> findByRole(User.Role role, Pageable pageable) {
        return userRepository.findByRole(role, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<User> searchUsers(String keyword, Pageable pageable) {
        return userRepository.findByUsernameContainingIgnoreCase(keyword, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getUserCount() {
        return userRepository.count();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getRecentUsers(int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        return userRepository.findAll(pageable).getContent();
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<User> getRecentUsers(Pageable pageable) {
        return userRepository.findRecentUsers(pageable);
    }
    
    @Override
    public User activateUser(Long userId) {
        logger.info("Activating user ID: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        // 这里可以添加激活逻辑，比如设置激活状态字段
        // user.setActive(true);
        
        return userRepository.save(user);
    }
    
    @Override
    public User deactivateUser(Long userId) {
        logger.info("Deactivating user ID: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        // 这里可以添加禁用逻辑，比如设置激活状态字段
        // user.setActive(false);
        
        return userRepository.save(user);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean validatePassword(Long userId, String rawPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }
    
    @Transactional(readOnly = true)
    public boolean validatePassword(User user, String rawPassword) {
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }
}