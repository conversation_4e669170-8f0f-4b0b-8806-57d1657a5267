<template>
  <div class="admin-view">
    <el-card class="admin-header">
      <h1>管理员控制台</h1>
      <p>欢迎使用电影推荐系统管理后台</p>
    </el-card>

    <el-tabs v-model="activeTab" class="admin-tabs">
      <!-- 电影管理 -->
      <el-tab-pane label="电影管理" name="movies">
        <div class="tab-content">
          <div class="content-header">
            <h2>电影管理</h2>
            <el-button type="primary" @click="showAddMovieDialog = true">
              <el-icon><Plus /></el-icon>
              添加电影
            </el-button>
          </div>
          
          <!-- 搜索和筛选 -->
          <div class="search-section">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-input
                  v-model="movieSearch.keyword"
                  placeholder="搜索电影标题"
                  @input="searchMovies"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="movieSearch.genre"
                  placeholder="选择类型"
                  @change="searchMovies"
                  clearable
                >
                  <el-option label="动作" value="动作" />
                  <el-option label="喜剧" value="喜剧" />
                  <el-option label="剧情" value="剧情" />
                  <el-option label="科幻" value="科幻" />
                  <el-option label="恐怖" value="恐怖" />
                  <el-option label="爱情" value="爱情" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="movieSearch.sortBy"
                  placeholder="排序方式"
                  @change="searchMovies"
                >
                  <el-option label="标题" value="title" />
                  <el-option label="上映年份" value="release_year" />
                  <el-option label="评分" value="average_rating" />
                  <el-option label="创建时间" value="created_at" />
                </el-select>
              </el-col>
            </el-row>
          </div>
          
          <!-- 电影列表 -->
          <el-table
            :data="movies"
            v-loading="moviesLoading"
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="title" label="标题" min-width="150" />
            <el-table-column prop="genre" label="类型" width="100" />
            <el-table-column prop="release_year" label="年份" width="100" />
            <el-table-column prop="duration" label="时长(分钟)" width="120" />
            <el-table-column prop="average_rating" label="评分" width="100">
              <template #default="{ row }">
                <el-rate
                  :model-value="row.average_rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="editMovie(row)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteMovie(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="moviePagination.page"
            :page-size="moviePagination.size"
            :total="moviePagination.total"
            layout="prev, pager, next, total"
            @current-change="loadMovies"
            class="pagination"
          />
        </div>
      </el-tab-pane>
      
      <!-- 用户管理 -->
      <el-tab-pane label="用户管理" name="users">
        <div class="tab-content">
          <div class="content-header">
            <h2>用户管理</h2>
          </div>
          
          <!-- 用户搜索 -->
          <div class="search-section">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-input
                  v-model="userSearch.keyword"
                  placeholder="搜索用户名或邮箱"
                  @input="searchUsers"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="userSearch.role"
                  placeholder="选择角色"
                  @change="searchUsers"
                  clearable
                >
                  <el-option label="普通用户" value="USER" />
                  <el-option label="管理员" value="ADMIN" />
                </el-select>
              </el-col>
            </el-row>
          </div>
          
          <!-- 用户列表 -->
          <el-table
            :data="users"
            v-loading="usersLoading"
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="用户名" min-width="120" />
            <el-table-column prop="email" label="邮箱" min-width="180" />
            <el-table-column prop="role" label="角色" width="100">
              <template #default="{ row }">
                <el-tag :type="row.role === 'ADMIN' ? 'danger' : 'primary'">
                  {{ row.role === 'ADMIN' ? '管理员' : '普通用户' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="注册时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button
                  v-if="row.role !== 'ADMIN'"
                  type="warning"
                  size="small"
                  @click="toggleUserRole(row)"
                >
                  设为管理员
                </el-button>
                <el-button
                  v-else
                  type="info"
                  size="small"
                  @click="toggleUserRole(row)"
                >
                  取消管理员
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="userPagination.page"
            :page-size="userPagination.size"
            :total="userPagination.total"
            layout="prev, pager, next, total"
            @current-change="loadUsers"
            class="pagination"
          />
        </div>
      </el-tab-pane>
      
      <!-- 系统统计 -->
      <el-tab-pane label="系统统计" name="stats">
        <div class="tab-content">
          <div class="content-header">
            <h2>系统统计</h2>
          </div>
          
          <el-row :gutter="20" class="stats-cards">
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.totalMovies }}</div>
                  <div class="stat-label">电影总数</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.totalUsers }}</div>
                  <div class="stat-label">用户总数</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.totalRatings }}</div>
                  <div class="stat-label">评分总数</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.averageRating.toFixed(1) }}</div>
                  <div class="stat-label">平均评分</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑电影对话框 -->
    <el-dialog
      v-model="showAddMovieDialog"
      :title="editingMovie ? '编辑电影' : '添加电影'"
      width="600px"
    >
      <el-form
        ref="movieFormRef"
        :model="movieForm"
        :rules="movieRules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="movieForm.title" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="movieForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="类型" prop="genre">
          <el-select v-model="movieForm.genre" placeholder="选择类型">
            <el-option label="动作" value="动作" />
            <el-option label="喜剧" value="喜剧" />
            <el-option label="剧情" value="剧情" />
            <el-option label="科幻" value="科幻" />
            <el-option label="恐怖" value="恐怖" />
            <el-option label="爱情" value="爱情" />
          </el-select>
        </el-form-item>
        <el-form-item label="上映年份" prop="release_year">
          <el-input-number
            v-model="movieForm.release_year"
            :min="1900"
            :max="new Date().getFullYear() + 5"
          />
        </el-form-item>
        <el-form-item label="时长(分钟)" prop="duration">
          <el-input-number v-model="movieForm.duration" :min="1" />
        </el-form-item>
        <el-form-item label="海报URL">
          <el-input v-model="movieForm.poster_url" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddMovieDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMovie" :loading="saving">
          {{ editingMovie ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { getAllMovies, createMovie, updateMovie, deleteMovie } from '@/api/movie'
import type { Movie, MovieCreateRequest } from '@/api/types'
import type { FormInstance, FormRules } from 'element-plus'

const activeTab = ref('movies')

// 电影管理相关
const movies = ref<Movie[]>([])
const moviesLoading = ref(false)
const movieSearch = reactive({
  keyword: '',
  genre: '',
  sortBy: 'created_at'
})
const moviePagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 用户管理相关
const users = ref<any[]>([])
const usersLoading = ref(false)
const userSearch = reactive({
  keyword: '',
  role: ''
})
const userPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  totalMovies: 0,
  totalUsers: 0,
  totalRatings: 0,
  averageRating: 0
})

// 电影表单
const showAddMovieDialog = ref(false)
const editingMovie = ref<Movie | null>(null)
const saving = ref(false)
const movieFormRef = ref<FormInstance>()
const movieForm = reactive<MovieCreateRequest>({
  title: '',
  description: '',
  genre: '',
  release_year: new Date().getFullYear(),
  duration: 0,
  poster_url: ''
})

const movieRules: FormRules = {
  title: [{ required: true, message: '请输入电影标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入电影描述', trigger: 'blur' }],
  genre: [{ required: true, message: '请选择电影类型', trigger: 'change' }],
  release_year: [{ required: true, message: '请输入上映年份', trigger: 'blur' }],
  duration: [{ required: true, message: '请输入电影时长', trigger: 'blur' }]
}

// 加载电影列表
const loadMovies = async () => {
  try {
    moviesLoading.value = true
    const response = await getAllMovies({
      ...movieSearch,
      page: moviePagination.page,
      size: moviePagination.size
    })
    movies.value = response.data.content || response.data
    if (response.data.totalElements) {
      moviePagination.total = response.data.totalElements
    }
  } catch (error) {
    console.error('加载电影列表失败:', error)
    ElMessage.error('加载电影列表失败')
  } finally {
    moviesLoading.value = false
  }
}

// 搜索电影
const searchMovies = () => {
  moviePagination.page = 1
  loadMovies()
}

// 编辑电影
const editMovie = (movie: Movie) => {
  editingMovie.value = movie
  Object.assign(movieForm, {
    title: movie.title,
    description: movie.description,
    genre: movie.genre,
    release_year: movie.release_year,
    duration: movie.duration,
    poster_url: movie.poster_url || ''
  })
  showAddMovieDialog.value = true
}

// 保存电影
const saveMovie = async () => {
  if (!movieFormRef.value) return
  
  try {
    await movieFormRef.value.validate()
    saving.value = true
    
    if (editingMovie.value) {
      await updateMovie({ ...movieForm, id: editingMovie.value.id })
      ElMessage.success('电影更新成功')
    } else {
      await createMovie(movieForm)
      ElMessage.success('电影添加成功')
    }
    
    showAddMovieDialog.value = false
    resetMovieForm()
    loadMovies()
  } catch (error) {
    console.error('保存电影失败:', error)
    ElMessage.error('保存电影失败')
  } finally {
    saving.value = false
  }
}

// 删除电影
const deleteMovie = async (movie: Movie) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除电影「${movie.title}」吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteMovie(movie.id)
    ElMessage.success('电影删除成功')
    loadMovies()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除电影失败:', error)
      ElMessage.error('删除电影失败')
    }
  }
}

// 重置电影表单
const resetMovieForm = () => {
  editingMovie.value = null
  Object.assign(movieForm, {
    title: '',
    description: '',
    genre: '',
    release_year: new Date().getFullYear(),
    duration: 0,
    poster_url: ''
  })
  movieFormRef.value?.clearValidate()
}

// 加载用户列表
const loadUsers = async () => {
  try {
    usersLoading.value = true
    // 这里应该调用获取用户列表的API
    // const response = await getAllUsers(userSearch, userPagination.page, userPagination.size)
    // users.value = response.data.content
    // userPagination.total = response.data.totalElements
    
    // 临时模拟数据
    users.value = []
    userPagination.total = 0
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

// 搜索用户
const searchUsers = () => {
  userPagination.page = 1
  loadUsers()
}

// 切换用户角色
const toggleUserRole = async (user: any) => {
  try {
    const newRole = user.role === 'ADMIN' ? 'USER' : 'ADMIN'
    const action = newRole === 'ADMIN' ? '设为管理员' : '取消管理员权限'
    
    await ElMessageBox.confirm(
      `确定要${action}用户「${user.username}」吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用更新用户角色的API
    // await updateUserRole(user.id, newRole)
    
    user.role = newRole
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户角色失败:', error)
      ElMessage.error('更新用户角色失败')
    }
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用获取统计数据的API
    // const response = await getSystemStats()
    // Object.assign(stats, response.data)
    
    // 临时模拟数据
    stats.totalMovies = movies.value.length
    stats.totalUsers = users.value.length
    stats.totalRatings = 0
    stats.averageRating = 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadMovies()
  loadUsers()
  loadStats()
})
</script>

<style scoped>
.admin-view {
  padding: 20px;
}

.admin-header {
  margin-bottom: 20px;
  text-align: center;
}

.admin-header h1 {
  margin: 0 0 10px 0;
  color: #333;
}

.admin-header p {
  margin: 0;
  color: #666;
}

.admin-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.tab-content {
  min-height: 600px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0;
  color: #333;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.stats-cards {
  margin-top: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 2.5em;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

@media (max-width: 768px) {
  .admin-view {
    padding: 10px;
  }
  
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .search-section .el-row {
    flex-direction: column;
  }
  
  .search-section .el-col {
    margin-bottom: 10px;
  }
}
</style>