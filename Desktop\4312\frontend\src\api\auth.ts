import api from './index'
import type { User, LoginRequest, RegisterRequest, LoginResponse, ApiResponse } from './types'

// 用户登录
export const login = (data: LoginRequest): Promise<LoginResponse> => {
  return api.post('/auth/login', data)
}

// 用户注册
export const register = (data: RegisterRequest): Promise<User> => {
  return api.post('/auth/register', data)
}

// 获取当前用户信息
export const getCurrentUser = (): Promise<User> => {
  return api.get('/auth/me')
}

// 更新用户信息
export const updateProfile = (data: Partial<User>): Promise<User> => {
  return api.put('/auth/profile', data)
}

// 修改密码
export const changePassword = (oldPassword: string, newPassword: string) => {
  return api.put('/auth/change-password', { oldPassword, newPassword })
}