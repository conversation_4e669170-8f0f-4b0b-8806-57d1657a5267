package edu.sbs.cs.controller;

import edu.sbs.cs.dto.ApiResponse;
import edu.sbs.cs.entity.Comment;
import edu.sbs.cs.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * 评论控制器
 * 处理评论相关的API请求
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@RestController
@RequestMapping("/api/comments")
@Tag(name = "评论管理", description = "电影评论相关接口")
public class CommentController {
    
    @Autowired
    private CommentService commentService;
    
    /**
     * 创建评论
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "创建评论", description = "用户对电影发表评论")
    public ResponseEntity<ApiResponse<Comment>> createComment(
            @Parameter(description = "电影ID", required = true) @RequestParam Long movieId,
            @Parameter(description = "评论内容", required = true) 
            @RequestParam @NotBlank(message = "评论内容不能为空") 
            @Size(max = 1000, message = "评论内容不能超过1000个字符") String content,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        Comment comment = commentService.createComment(userId, movieId, content);
        
        return ResponseEntity.ok(ApiResponse.success("评论发表成功", comment));
    }
    
    /**
     * 根据ID获取评论
     */
    @GetMapping("/{commentId}")
    @Operation(summary = "获取评论详情", description = "根据ID获取评论详情")
    public ResponseEntity<ApiResponse<Comment>> getCommentById(
            @Parameter(description = "评论ID", required = true) @PathVariable Long commentId) {
        
        Optional<Comment> comment = commentService.findById(commentId);
        
        if (comment.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("获取评论成功", comment.get()));
        } else {
            return ResponseEntity.ok(ApiResponse.error("评论不存在", 404));
        }
    }
    
    /**
     * 获取电影的评论列表
     */
    @GetMapping("/movie/{movieId}")
    @Operation(summary = "获取电影评论", description = "分页查询电影的评论列表")
    public ResponseEntity<ApiResponse<Page<Comment>>> getMovieComments(
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size,
            @Parameter(description = "排序方式", example = "time") @RequestParam(defaultValue = "time") String sortBy) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Comment> comments;
        
        switch (sortBy.toLowerCase()) {
            case "likes":
                comments = commentService.findByMovieIdOrderByLikes(movieId, pageable);
                break;
            case "time":
            default:
                comments = commentService.findByMovieIdOrderByCreatedAt(movieId, pageable);
                break;
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取电影评论成功", comments));
    }
    
    /**
     * 获取用户的评论列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户评论", description = "分页查询用户的评论列表")
    public ResponseEntity<ApiResponse<Page<Comment>>> getUserComments(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Comment> comments = commentService.findByUserIdOrderByCreatedAt(userId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户评论成功", comments));
    }
    
    /**
     * 获取热门评论
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门评论", description = "按点赞数排序获取热门评论")
    public ResponseEntity<ApiResponse<Page<Comment>>> getPopularComments(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Comment> comments = commentService.findPopularComments(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取热门评论成功", comments));
    }
    
    /**
     * 获取最新评论
     */
    @GetMapping("/latest")
    @Operation(summary = "获取最新评论", description = "按时间排序获取最新评论")
    public ResponseEntity<ApiResponse<Page<Comment>>> getLatestComments(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Comment> comments = commentService.findLatestComments(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取最新评论成功", comments));
    }
    
    /**
     * 搜索评论
     */
    @GetMapping("/search")
    @Operation(summary = "搜索评论", description = "根据关键词搜索评论内容")
    public ResponseEntity<ApiResponse<Page<Comment>>> searchComments(
            @Parameter(description = "搜索关键词", required = true) @RequestParam @NotBlank String keyword,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Comment> comments = commentService.searchByContent(keyword, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("搜索评论成功", comments));
    }
    
    /**
     * 获取电影的热门评论
     */
    @GetMapping("/movie/{movieId}/top")
    @Operation(summary = "获取电影热门评论", description = "获取电影的热门评论")
    public ResponseEntity<ApiResponse<Page<Comment>>> getTopMovieComments(
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "5") @RequestParam(defaultValue = "5") @Min(1) @Max(50) int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Comment> comments = commentService.findTopCommentsByMovie(movieId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取电影热门评论成功", comments));
    }
    
    /**
     * 更新评论
     */
    @PutMapping("/{commentId}")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "更新评论", description = "用户更新自己的评论内容")
    public ResponseEntity<ApiResponse<Comment>> updateComment(
            @Parameter(description = "评论ID", required = true) @PathVariable Long commentId,
            @Parameter(description = "新的评论内容", required = true) 
            @RequestParam @NotBlank(message = "评论内容不能为空") 
            @Size(max = 1000, message = "评论内容不能超过1000个字符") String content,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        
        // 检查用户是否有权限编辑该评论
        if (!commentService.canUserEditComment(userId, commentId)) {
            return ResponseEntity.ok(ApiResponse.error("无权限编辑该评论", 403));
        }
        
        Comment updatedComment = commentService.updateComment(commentId, content);
        
        return ResponseEntity.ok(ApiResponse.success("评论更新成功", updatedComment));
    }
    
    /**
     * 点赞评论
     */
    @PostMapping("/{commentId}/like")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "点赞评论", description = "用户点赞评论")
    public ResponseEntity<ApiResponse<Comment>> likeComment(
            @Parameter(description = "评论ID", required = true) @PathVariable Long commentId) {
        
        Comment comment = commentService.likeComment(commentId);
        
        return ResponseEntity.ok(ApiResponse.success("点赞成功", comment));
    }
    
    /**
     * 取消点赞评论
     */
    @DeleteMapping("/{commentId}/like")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "取消点赞评论", description = "用户取消点赞评论")
    public ResponseEntity<ApiResponse<Comment>> unlikeComment(
            @Parameter(description = "评论ID", required = true) @PathVariable Long commentId) {
        
        Comment comment = commentService.unlikeComment(commentId);
        
        return ResponseEntity.ok(ApiResponse.success("取消点赞成功", comment));
    }
    
    /**
     * 删除评论
     */
    @DeleteMapping("/{commentId}")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "删除评论", description = "用户删除自己的评论")
    public ResponseEntity<ApiResponse<String>> deleteComment(
            @Parameter(description = "评论ID", required = true) @PathVariable Long commentId,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        
        // 检查用户是否有权限删除该评论
        if (!commentService.canUserDeleteComment(userId, commentId)) {
            return ResponseEntity.ok(ApiResponse.error("无权限删除该评论", 403));
        }
        
        boolean deleted = commentService.deleteComment(commentId);
        
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success("删除评论成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.error("评论不存在或删除失败"));
        }
    }
    
    /**
     * 获取评论统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取评论统计", description = "获取系统整体的评论统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCommentStats() {
        
        Map<String, Object> stats = commentService.getCommentStats();
        
        return ResponseEntity.ok(ApiResponse.success("获取评论统计成功", stats));
    }
    
    /**
     * 获取用户评论统计
     */
    @GetMapping("/user/{userId}/stats")
    @Operation(summary = "获取用户评论统计", description = "获取用户的评论统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserCommentStats(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        Map<String, Object> stats = commentService.getUserCommentStats(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户评论统计成功", stats));
    }
    
    /**
     * 获取电影评论统计
     */
    @GetMapping("/movie/{movieId}/stats")
    @Operation(summary = "获取电影评论统计", description = "获取电影的评论统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMovieCommentStats(
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId) {
        
        Map<String, Object> stats = commentService.getMovieCommentStats(movieId);
        
        return ResponseEntity.ok(ApiResponse.success("获取电影评论统计成功", stats));
    }
    
    /**
     * 检查用户是否已评论
     */
    @GetMapping("/check")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "检查是否已评论", description = "检查用户是否已对电影评论")
    public ResponseEntity<ApiResponse<Boolean>> checkUserComment(
            @Parameter(description = "电影ID", required = true) @RequestParam Long movieId,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        boolean hasCommented = commentService.hasUserCommentedOnMovie(userId, movieId);
        
        return ResponseEntity.ok(ApiResponse.success("检查评论状态成功", hasCommented));
    }
    
    /**
     * 根据时间范围查询评论
     */
    @GetMapping("/time-range")
    @Operation(summary = "按时间范围查询评论", description = "查询指定时间范围内的评论")
    public ResponseEntity<ApiResponse<Page<Comment>>> getCommentsByTimeRange(
            @Parameter(description = "开始时间", required = true, example = "2024-01-01T00:00:00") @RequestParam String startTime,
            @Parameter(description = "结束时间", required = true, example = "2024-12-31T23:59:59") @RequestParam String endTime,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        LocalDateTime start = LocalDateTime.parse(startTime);
        LocalDateTime end = LocalDateTime.parse(endTime);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Comment> comments = commentService.findByCreatedAtBetween(start, end, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取时间范围评论成功", comments));
    }
    
    /**
     * 管理员删除评论
     */
    @DeleteMapping("/admin/{commentId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "管理员删除评论", description = "管理员删除任意评论")
    public ResponseEntity<ApiResponse<String>> adminDeleteComment(
            @Parameter(description = "评论ID", required = true) @PathVariable Long commentId) {
        
        boolean deleted = commentService.deleteComment(commentId);
        
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success("删除评论成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.error("评论不存在或删除失败"));
        }
    }
}