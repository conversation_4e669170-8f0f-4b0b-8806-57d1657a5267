package edu.sbs.cs.dto;

import edu.sbs.cs.entity.Movie;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 电影数据传输对象
 * 用于API响应，避免实体类的懒加载问题
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
public class MovieDTO {
    
    private Long id;
    private String title;
    private String director;
    private String actors;
    private Movie.Genre genre;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate releaseDate;
    
    private Integer duration;
    private String description;
    private String posterUrl;
    private String trailerUrl;
    private BigDecimal imdbRating;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // 计算得出的评分信息
    private Double averageRating;
    private Long ratingCount;
    
    // 默认构造函数
    public MovieDTO() {}
    
    // 从Movie实体创建DTO的构造函数
    public MovieDTO(Movie movie) {
        this.id = movie.getId();
        this.title = movie.getTitle();
        this.director = movie.getDirector();
        this.actors = movie.getActors();
        this.genre = movie.getGenre();
        this.releaseDate = movie.getReleaseDate();
        this.duration = movie.getDuration();
        this.description = movie.getDescription();
        this.posterUrl = movie.getPosterUrl();
        this.trailerUrl = movie.getTrailerUrl();
        this.imdbRating = movie.getImdbRating();
        this.createdAt = movie.getCreatedAt();
        this.updatedAt = movie.getUpdatedAt();
    }
    
    // 从Movie实体创建DTO，包含评分信息
    public MovieDTO(Movie movie, Double averageRating, Long ratingCount) {
        this(movie);
        this.averageRating = averageRating;
        this.ratingCount = ratingCount;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDirector() {
        return director;
    }
    
    public void setDirector(String director) {
        this.director = director;
    }
    
    public String getActors() {
        return actors;
    }
    
    public void setActors(String actors) {
        this.actors = actors;
    }
    
    public Movie.Genre getGenre() {
        return genre;
    }
    
    public void setGenre(Movie.Genre genre) {
        this.genre = genre;
    }
    
    public LocalDate getReleaseDate() {
        return releaseDate;
    }
    
    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }
    
    public Integer getDuration() {
        return duration;
    }
    
    public void setDuration(Integer duration) {
        this.duration = duration;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getPosterUrl() {
        return posterUrl;
    }
    
    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }
    
    public String getTrailerUrl() {
        return trailerUrl;
    }
    
    public void setTrailerUrl(String trailerUrl) {
        this.trailerUrl = trailerUrl;
    }
    
    public BigDecimal getImdbRating() {
        return imdbRating;
    }
    
    public void setImdbRating(BigDecimal imdbRating) {
        this.imdbRating = imdbRating;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Double getAverageRating() {
        return averageRating;
    }
    
    public void setAverageRating(Double averageRating) {
        this.averageRating = averageRating;
    }
    
    public Long getRatingCount() {
        return ratingCount;
    }
    
    public void setRatingCount(Long ratingCount) {
        this.ratingCount = ratingCount;
    }
}