package edu.sbs.cs.service.impl;

import edu.sbs.cs.entity.Comment;
import edu.sbs.cs.entity.User;
import edu.sbs.cs.entity.Movie;
import edu.sbs.cs.repository.CommentRepository;
import edu.sbs.cs.repository.UserRepository;
import edu.sbs.cs.repository.MovieRepository;
import edu.sbs.cs.service.CommentService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 评论服务实现类
 * 实现评论相关的业务逻辑
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Service
@Transactional
public class CommentServiceImpl implements CommentService {
    
    @Autowired
    private CommentRepository commentRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MovieRepository movieRepository;
    
    @Override
    public Comment createComment(Long userId, Long movieId, String content) {
        if (userId == null || movieId == null) {
            throw new IllegalArgumentException("用户ID和电影ID不能为空");
        }
        
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("评论内容不能为空");
        }
        
        if (content.length() > 1000) {
            throw new IllegalArgumentException("评论内容不能超过1000个字符");
        }
        
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new EntityNotFoundException("用户不存在"));
        Movie movie = movieRepository.findById(movieId)
            .orElseThrow(() -> new EntityNotFoundException("电影不存在"));
        
        Comment comment = new Comment();
        comment.setUser(user);
        comment.setMovie(movie);
        comment.setContent(content.trim());
        comment.setLikes(0);
        
        return commentRepository.save(comment);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<Comment> findById(Long commentId) {
        if (commentId == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        return commentRepository.findById(commentId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByMovieId(Long movieId, Pageable pageable) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return commentRepository.findByMovieId(movieId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByUserId(Long userId, Pageable pageable) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return commentRepository.findByUserId(userId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByMovieIdOrderByLikes(Long movieId, Pageable pageable) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return commentRepository.findByMovieIdOrderByLikesDesc(movieId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByMovieIdOrderByCreatedAt(Long movieId, Pageable pageable) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return commentRepository.findByMovieIdOrderByCreatedAtDesc(movieId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByUserIdOrderByCreatedAt(Long userId, Pageable pageable) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return commentRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long countByMovieId(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return commentRepository.countByMovieId(movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long countByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return commentRepository.countByUserId(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> searchByContent(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            throw new IllegalArgumentException("搜索关键词不能为空");
        }
        return commentRepository.findByContentContainingIgnoreCase(keyword.trim(), pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return commentRepository.findByCreatedAtBetween(startTime, endTime, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findPopularComments(Pageable pageable) {
        return commentRepository.findPopularComments(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findLatestComments(Pageable pageable) {
        return commentRepository.findLatestComments(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findByUserIdAndMovieId(Long userId, Long movieId, Pageable pageable) {
        if (userId == null || movieId == null) {
            throw new IllegalArgumentException("用户ID和电影ID不能为空");
        }
        return commentRepository.findByUserIdAndMovieId(userId, movieId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findTopCommentsByMovie(Long movieId, Pageable pageable) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return commentRepository.findTopCommentsByMovie(movieId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> findRecentCommentsByUser(Long userId, Pageable pageable) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return commentRepository.findRecentCommentsByUser(userId, pageable);
    }
    
    @Override
    public Comment updateComment(Long commentId, String content) {
        if (commentId == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("评论内容不能为空");
        }
        
        if (content.length() > 1000) {
            throw new IllegalArgumentException("评论内容不能超过1000个字符");
        }
        
        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("评论不存在"));
        
        comment.setContent(content.trim());
        comment.setUpdatedAt(LocalDateTime.now());
        
        return commentRepository.save(comment);
    }
    
    @Override
    public Comment likeComment(Long commentId) {
        if (commentId == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        
        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("评论不存在"));
        
        comment.setLikes(comment.getLikes() + 1);
        comment.setUpdatedAt(LocalDateTime.now());
        
        return commentRepository.save(comment);
    }
    
    @Override
    public Comment unlikeComment(Long commentId) {
        if (commentId == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        
        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("评论不存在"));
        
        if (comment.getLikes() > 0) {
            comment.setLikes(comment.getLikes() - 1);
            comment.setUpdatedAt(LocalDateTime.now());
        }
        
        return commentRepository.save(comment);
    }
    
    @Override
    public boolean deleteComment(Long commentId) {
        if (commentId == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        
        if (commentRepository.existsById(commentId)) {
            commentRepository.deleteById(commentId);
            return true;
        }
        return false;
    }
    
    @Override
    public Long deleteByMovieId(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        Long count = commentRepository.countByMovieId(movieId);
        commentRepository.deleteByMovieId(movieId);
        return count;
    }
    
    @Override
    public Long deleteByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        Long count = commentRepository.countByUserId(userId);
        commentRepository.deleteByUserId(userId);
        return count;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getCommentStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总评论数
        Long totalComments = commentRepository.count();
        stats.put("totalComments", totalComments);
        
        if (totalComments > 0) {
            // 今日评论数
            LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfDay = startOfDay.plusDays(1);
            Long todayComments = commentRepository.countByCreatedAtBetween(startOfDay, endOfDay);
            stats.put("todayComments", todayComments);
            
            // 平均点赞数
            Double averageLikes = commentRepository.findAverageLikes();
            stats.put("averageLikes", averageLikes != null ? Math.round(averageLikes * 10.0) / 10.0 : 0.0);
            
            // 最高点赞数
            Integer maxLikes = commentRepository.findMaxLikes();
            stats.put("maxLikes", maxLikes != null ? maxLikes : 0);
        } else {
            stats.put("todayComments", 0L);
            stats.put("averageLikes", 0.0);
            stats.put("maxLikes", 0);
        }
        
        return stats;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserCommentStats(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总评论数
        Long totalComments = commentRepository.countByUserId(userId);
        stats.put("totalComments", totalComments);
        
        if (totalComments > 0) {
            // 总点赞数
            Long totalLikes = commentRepository.sumLikesByUserId(userId);
            stats.put("totalLikes", totalLikes != null ? totalLikes : 0L);
            
            // 平均点赞数
            Double averageLikes = totalLikes != null && totalComments > 0 ? 
                (double) totalLikes / totalComments : 0.0;
            stats.put("averageLikes", Math.round(averageLikes * 10.0) / 10.0);
            
            // 最高点赞数
            Integer maxLikes = commentRepository.findMaxLikesByUserId(userId);
            stats.put("maxLikes", maxLikes != null ? maxLikes : 0);
        } else {
            stats.put("totalLikes", 0L);
            stats.put("averageLikes", 0.0);
            stats.put("maxLikes", 0);
        }
        
        return stats;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getMovieCommentStats(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总评论数
        Long totalComments = commentRepository.countByMovieId(movieId);
        stats.put("totalComments", totalComments);
        
        if (totalComments > 0) {
            // 总点赞数
            Long totalLikes = commentRepository.sumLikesByMovieId(movieId);
            stats.put("totalLikes", totalLikes != null ? totalLikes : 0L);
            
            // 平均点赞数
            Double averageLikes = totalLikes != null && totalComments > 0 ? 
                (double) totalLikes / totalComments : 0.0;
            stats.put("averageLikes", Math.round(averageLikes * 10.0) / 10.0);
            
            // 最高点赞数
            Integer maxLikes = commentRepository.findMaxLikesByMovieId(movieId);
            stats.put("maxLikes", maxLikes != null ? maxLikes : 0);
        } else {
            stats.put("totalLikes", 0L);
            stats.put("averageLikes", 0.0);
            stats.put("maxLikes", 0);
        }
        
        return stats;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean hasUserCommentedOnMovie(Long userId, Long movieId) {
        if (userId == null || movieId == null) {
            throw new IllegalArgumentException("用户ID和电影ID不能为空");
        }
        return commentRepository.existsByUserIdAndMovieId(userId, movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long getTotalCommentCount() {
        return commentRepository.count();
    }
    
    @Override
    public List<Comment> createComments(List<Comment> comments) {
        if (comments == null || comments.isEmpty()) {
            throw new IllegalArgumentException("评论列表不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (Comment comment : comments) {
            if (comment.getCreatedAt() == null) {
                comment.setCreatedAt(now);
            }
            if (comment.getUpdatedAt() == null) {
                comment.setUpdatedAt(now);
            }
            if (comment.getLikes() == null) {
                comment.setLikes(0);
            }
        }
        
        return commentRepository.saveAll(comments);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Comment> searchByMovieIdAndContent(Long movieId, String keyword, Pageable pageable) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        if (keyword == null || keyword.trim().isEmpty()) {
            throw new IllegalArgumentException("搜索关键词不能为空");
        }
        return commentRepository.findByMovieIdAndContentContainingIgnoreCase(movieId, keyword.trim(), pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long getReplyCount(Long commentId) {
        if (commentId == null) {
            throw new IllegalArgumentException("评论ID不能为空");
        }
        // 这里假设暂时不支持回复功能，返回0
        // 如果后续需要支持回复功能，可以扩展Comment实体和相关逻辑
        return 0L;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean canUserDeleteComment(Long userId, Long commentId) {
        if (userId == null || commentId == null) {
            throw new IllegalArgumentException("用户ID和评论ID不能为空");
        }
        
        Optional<Comment> comment = commentRepository.findById(commentId);
        if (comment.isPresent()) {
            // 用户只能删除自己的评论
            return comment.get().getUser().getId().equals(userId);
        }
        return false;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean canUserEditComment(Long userId, Long commentId) {
        if (userId == null || commentId == null) {
            throw new IllegalArgumentException("用户ID和评论ID不能为空");
        }
        
        Optional<Comment> comment = commentRepository.findById(commentId);
        if (comment.isPresent()) {
            // 用户只能编辑自己的评论
            return comment.get().getUser().getId().equals(userId);
        }
        return false;
    }
}