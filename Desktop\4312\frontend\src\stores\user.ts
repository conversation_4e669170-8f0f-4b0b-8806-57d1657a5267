import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '../api/types'
import { login as apiLogin, register as apiRegister, getCurrentUser } from '../api/auth'
import api from '../api/index'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')
  
  // 登录
  const login = async (username: string, password: string) => {
    try {
      const response = await apiLogin({ username, password })
      token.value = response.token
      user.value = response.user
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 注册
  const register = async (username: string, email: string, password: string) => {
    try {
      const newUser = await apiRegister({ username, email, password })
      return newUser
    } catch (error) {
      throw error
    }
  }
  
  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('user')
    localStorage.removeItem('token')
  }

  const forgotPassword = async (email: string) => {
    try {
      const response = await api.post('/auth/forgot-password', { email })
      return { success: true, message: response.data?.message || '密码重置邮件已发送' }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '发送重置邮件失败，请稍后重试' 
      }
    }
  }
  
  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      try {
        const userData = await getCurrentUser()
        user.value = userData
        localStorage.setItem('user', JSON.stringify(userData))
      } catch (error: any) {
        // 只有在401错误时才清除token，其他错误保持登录状态
        if (error.response?.status === 401) {
          logout()
        }
        throw error
      }
    }
  }
  
  // 从本地存储恢复用户信息
  const restoreUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && savedUser !== 'undefined' && savedUser !== 'null') {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        logout()
      }
    } else if (savedUser === 'undefined' || savedUser === 'null') {
      // 清理无效的localStorage数据
      localStorage.removeItem('user')
      localStorage.removeItem('token')
    }
  }
  
  return {
    user,
    token,
    isLoggedIn,
    isAdmin,
    login,
    register,
    logout,
    forgotPassword,
    initUser,
    restoreUser
  }
})