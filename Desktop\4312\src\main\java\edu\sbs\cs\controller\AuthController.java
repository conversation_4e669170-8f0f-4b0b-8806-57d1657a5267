package edu.sbs.cs.controller;

import edu.sbs.cs.dto.*;
import edu.sbs.cs.entity.User;
import edu.sbs.cs.service.UserService;
import edu.sbs.cs.service.UserDetailsServiceImpl;
import edu.sbs.cs.util.JwtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 处理用户登录、注册等认证相关的API请求
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Tag(name = "认证管理", description = "用户认证相关API")
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Operation(summary = "用户登录", description = "用户登录获取JWT令牌")
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<JwtResponse>> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        logger.info("User login attempt: {}", loginRequest.getUsername());
        
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
                )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtils.generateJwtToken(authentication);
            
            // 从认证对象中获取用户信息，避免重复数据库查询
            UserDetailsServiceImpl.UserPrincipal userPrincipal = 
                (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            
            // 获取完整的用户信息（只查询一次数据库）
            User user = userService.findById(userPrincipal.getId())
                .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            // 计算令牌过期时间
            long expiresIn = jwtUtils.getRemainingValidityTime(jwt);
            
            JwtResponse jwtResponse = new JwtResponse(jwt, user, expiresIn);
            
            logger.info("User logged in successfully: {}", loginRequest.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登录成功", jwtResponse));
            
        } catch (Exception e) {
            logger.error("Login failed for user: {}, error: {}", loginRequest.getUsername(), e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("登录失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "用户注册", description = "注册新用户账号")
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<User>> registerUser(@Valid @RequestBody RegisterRequest registerRequest) {
        logger.info("User registration attempt: {}", registerRequest.getUsername());
        
        try {
            // 检查用户名是否已存在
            if (userService.existsByUsername(registerRequest.getUsername())) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("用户名已存在"));
            }
            
            // 检查邮箱是否已存在
            if (userService.existsByEmail(registerRequest.getEmail())) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("邮箱已存在"));
            }
            
            // 注册用户
            User user = userService.registerUser(registerRequest);
            
            // 清除密码字段
            user.setPassword(null);
            
            logger.info("User registered successfully: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("注册成功", user));
            
        } catch (Exception e) {
            logger.error("Registration failed for user: {}, error: {}", registerRequest.getUsername(), e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("注册失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "刷新令牌", description = "刷新JWT令牌")
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<JwtResponse>> refreshToken(@RequestHeader("Authorization") String authHeader) {
        logger.info("Token refresh attempt");
        
        try {
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            
            if (token == null || !jwtUtils.validateJwtToken(token)) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("无效的令牌"));
            }
            
            String username = jwtUtils.getUserNameFromJwtToken(token);
            User user = userService.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String newToken = jwtUtils.generateTokenFromUsername(username);
            long expiresIn = jwtUtils.getRemainingValidityTime(newToken);
            
            JwtResponse jwtResponse = new JwtResponse(newToken, user, expiresIn);
            
            logger.info("Token refreshed successfully for user: {}", username);
            return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", jwtResponse));
            
        } catch (Exception e) {
            logger.error("Token refresh failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("令牌刷新失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "检查用户名可用性", description = "检查用户名是否已被使用")
    @GetMapping("/check-username")
    public ResponseEntity<ApiResponse<Boolean>> checkUsernameAvailability(@RequestParam String username) {
        boolean isAvailable = !userService.existsByUsername(username);
        return ResponseEntity.ok(ApiResponse.success("检查完成", isAvailable));
    }
    
    @Operation(summary = "检查邮箱可用性", description = "检查邮箱是否已被使用")
    @GetMapping("/check-email")
    public ResponseEntity<ApiResponse<Boolean>> checkEmailAvailability(@RequestParam String email) {
        boolean isAvailable = !userService.existsByEmail(email);
        return ResponseEntity.ok(ApiResponse.success("检查完成", isAvailable));
    }
    
    @Operation(summary = "验证令牌", description = "验证JWT令牌是否有效")
    @PostMapping("/validate")
    public ResponseEntity<ApiResponse<Boolean>> validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            boolean isValid = token != null && jwtUtils.validateJwtToken(token);
            
            return ResponseEntity.ok(ApiResponse.success("验证完成", isValid));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.success("验证完成", false));
        }
    }
    
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<User>> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated() || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                return ResponseEntity.status(401)
                    .body(ApiResponse.error("用户未登录"));
            }
            
            String username = authentication.getName();
            User user = userService.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            // 清除密码字段
            user.setPassword(null);
            
            logger.info("Get current user info: {}", username);
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user));
            
        } catch (Exception e) {
            logger.error("Get current user failed: {}", e.getMessage());
            return ResponseEntity.status(401)
                .body(ApiResponse.error("获取用户信息失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "用户登出", description = "用户登出（客户端清除令牌）")
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout() {
        // JWT是无状态的，登出主要由客户端处理（清除本地存储的令牌）
        // 这里可以添加一些服务端的登出逻辑，比如将令牌加入黑名单
        SecurityContextHolder.clearContext();
        
        logger.info("User logout");
        return ResponseEntity.ok(ApiResponse.success("登出成功"));
    }
}