<template>
  <div class="login-container">
    <!-- 左侧装饰区域 -->
    <div class="login-decoration">
      <div class="decoration-content">
        <div class="logo-section">
          <el-icon size="48" class="logo-icon"><VideoPlay /></el-icon>
          <h1 class="system-title">电影推荐系统</h1>
          <p class="system-subtitle">发现你的下一部最爱电影</p>
        </div>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Star /></el-icon>
            <span>个性化推荐</span>
          </div>
          <div class="feature-item">
            <el-icon><ChatDotRound /></el-icon>
            <span>影评互动</span>
          </div>
          <div class="feature-item">
            <el-icon><Search /></el-icon>
            <span>智能搜索</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧登录区域 -->
    <div class="login-section">
      <div class="login-card">
        <div class="login-header">
          <h2>登录</h2>
          <p>欢迎回到电影推荐系统</p>
        </div>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          @submit.prevent="handleLogin"
          :validate-on-rule-change="false"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名（6-20个字符）"
              size="large"
              prefix-icon="User"
              clearable
              :class="{ 'input-error': usernameError }"
              @blur="validateUsername"
              @input="clearUsernameError"
            />
            <div v-if="usernameError" class="error-message">
              <el-icon><WarningFilled /></el-icon>
              {{ usernameError }}
            </div>
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码（6-20个字符）"
              size="large"
              prefix-icon="Lock"
              show-password
              clearable
              :class="{ 'input-error': passwordError }"
              @keyup.enter="handleLogin"
              @blur="validatePassword"
              @input="clearPasswordError"
            />
            <div v-if="passwordError" class="error-message">
              <el-icon><WarningFilled /></el-icon>
              {{ passwordError }}
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loading"
              :disabled="!isFormValid"
              @click="handleLogin"
            >
              <template v-if="loading">
                <el-icon class="loading-icon"><Loading /></el-icon>
                登录中...
              </template>
              <template v-else>
                登录
              </template>
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="login-options">
          <el-button link class="forgot-password" @click="showForgotPassword">
            忘记密码？
          </el-button>
        </div>
        
        <div class="login-footer">
          <p>还没有账号？ <router-link to="/register" class="register-link">立即注册</router-link></p>
        </div>
      </div>
    </div>
    
    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      :before-close="closeForgotPassword"
    >
      <el-form ref="forgotFormRef" :model="forgotForm" :rules="forgotRules">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="forgotForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="forgotForm.email" placeholder="请输入注册邮箱" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeForgotPassword">取消</el-button>
          <el-button type="primary" :loading="resetLoading" @click="handleForgotPassword">
            发送重置邮件
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '../stores/user'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const forgotFormRef = ref<FormInstance>()
const loading = ref(false)
const resetLoading = ref(false)
const forgotPasswordVisible = ref(false)

// 实时验证错误信息
const usernameError = ref('')
const passwordError = ref('')

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 忘记密码表单
const forgotForm = reactive({
  username: '',
  email: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 6, max: 20, message: '用户名长度需在 6-20 个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度需在 6-20 个字符之间', trigger: 'blur' }
  ]
}

// 忘记密码验证规则
const forgotRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 计算表单是否有效
const isFormValid = computed(() => {
  return loginForm.username.length >= 6 && 
         loginForm.username.length <= 20 &&
         loginForm.password.length >= 6 && 
         loginForm.password.length <= 20 &&
         !usernameError.value &&
         !passwordError.value
})

// 实时验证用户名
const validateUsername = () => {
  const username = loginForm.username.trim()
  if (!username) {
    usernameError.value = '用户名不能为空'
  } else if (username.length < 6) {
    usernameError.value = '用户名至少需要6个字符'
  } else if (username.length > 20) {
    usernameError.value = '用户名不能超过20个字符'
  } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    usernameError.value = '用户名只能包含字母、数字和下划线'
  } else {
    usernameError.value = ''
  }
}

// 实时验证密码
const validatePassword = () => {
  const password = loginForm.password
  if (!password) {
    passwordError.value = '密码不能为空'
  } else if (password.length < 6) {
    passwordError.value = '密码至少需要6个字符'
  } else if (password.length > 20) {
    passwordError.value = '密码不能超过20个字符'
  } else {
    passwordError.value = ''
  }
}

// 清除用户名错误
const clearUsernameError = () => {
  if (usernameError.value) {
    usernameError.value = ''
  }
}

// 清除密码错误
const clearPasswordError = () => {
  if (passwordError.value) {
    passwordError.value = ''
  }
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordVisible.value = true
  // 重置表单
  forgotForm.username = ''
  forgotForm.email = ''
}

// 关闭忘记密码对话框
const closeForgotPassword = () => {
  forgotPasswordVisible.value = false
}

// 处理忘记密码
const handleForgotPassword = async () => {
  if (!forgotFormRef.value) return
  
  try {
    await forgotFormRef.value.validate()
    resetLoading.value = true
    
    // 模拟发送重置邮件的API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('重置密码邮件已发送，请查收邮箱')
    closeForgotPassword()
  } catch (error) {
    ElMessage.error('发送失败，请检查用户名和邮箱是否正确')
  } finally {
    resetLoading.value = false
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value || !isFormValid.value) return
  
  // 先进行实时验证
  validateUsername()
  validatePassword()
  
  if (usernameError.value || passwordError.value) {
    return
  }
  
  loading.value = true
  
  try {
    await loginFormRef.value.validate()
    
    const result = await userStore.login(loginForm.username, loginForm.password)
    
    if (result) {
      ElMessage.success('登录成功！')
      // 确保跳转到首页
      await router.push('/')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    
    // 检查是否是网络连接错误
    if (error.code === 'ECONNREFUSED' || error.message?.includes('Network Error') || !error.response) {
      ElMessage.error('无法连接到服务器，请确保后端服务已启动')
      return
    }
    
    const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码'
    ElMessage.error(errorMessage)
    
    // 如果是用户名或密码错误，显示相应的错误信息
    if (errorMessage.includes('用户名')) {
      usernameError.value = '用户名不存在'
    } else if (errorMessage.includes('密码')) {
      passwordError.value = '密码错误'
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* 左侧装饰区域 */
.login-decoration {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
  overflow: hidden;
}

.login-decoration::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  opacity: 0.3;
}

.decoration-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  max-width: 500px;
}

.logo-section {
  margin-bottom: 60px;
}

.logo-icon {
  color: white;
  margin-bottom: 20px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.system-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.system-subtitle {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  font-size: 1.1rem;
  font-weight: 500;
  opacity: 0.9;
  transition: all 0.3s ease;
}

.feature-item:hover {
  opacity: 1;
  transform: translateX(10px);
}

.feature-item .el-icon {
  font-size: 1.5rem;
}

/* 右侧登录区域 */
.login-section {
  flex: 0 0 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.login-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 50px 40px;
  width: 100%;
  max-width: 420px;
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h2 {
  color: #1a1a1a;
  margin: 0 0 12px 0;
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 15px;
  font-weight: 400;
}

.login-form {
  margin-bottom: 25px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-icon {
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.login-options {
  text-align: center;
  margin-bottom: 25px;
}

.forgot-password {
  color: #667eea;
  font-size: 14px;
  padding: 0;
  height: auto;
  font-weight: 500;
}

.forgot-password:hover {
  color: #764ba2;
}

.login-footer {
  text-align: center;
  padding-top: 25px;
  border-top: 1px solid #f0f0f0;
}

.login-footer p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* 错误信息样式 */
.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #f56565;
  font-size: 13px;
  margin-top: 8px;
  font-weight: 500;
}

.error-message .el-icon {
  font-size: 14px;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 25px;
}

:deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e1e5e9;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

:deep(.input-error .el-input__wrapper) {
  border-color: #f56565;
  box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
}

:deep(.el-input__inner) {
  color: #2d3748;
  font-weight: 500;
}

:deep(.el-input__inner::placeholder) {
  color: #a0aec0;
  font-weight: 400;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-decoration {
    flex: 0 0 auto;
    min-height: 300px;
    padding: 30px 20px;
  }
  
  .system-title {
    font-size: 2.5rem;
  }
  
  .feature-list {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .login-section {
    flex: 1;
    background: white;
    padding: 30px 20px;
  }
  
  .login-card {
    box-shadow: none;
    padding: 30px 20px;
  }
}

@media (max-width: 768px) {
  .login-decoration {
    min-height: 250px;
    padding: 20px;
  }
  
  .system-title {
    font-size: 2rem;
  }
  
  .system-subtitle {
    font-size: 1rem;
  }
  
  .feature-list {
    gap: 15px;
  }
  
  .feature-item {
    font-size: 1rem;
  }
  
  .login-section {
    padding: 20px;
  }
  
  .login-card {
    padding: 25px 20px;
  }
  
  .login-header h2 {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .login-decoration {
    min-height: 200px;
    padding: 15px;
  }
  
  .system-title {
    font-size: 1.75rem;
  }
  
  .feature-list {
    flex-direction: column;
    gap: 12px;
  }
  
  .login-section {
    padding: 15px;
  }
  
  .login-card {
    padding: 20px 15px;
  }
}
</style>