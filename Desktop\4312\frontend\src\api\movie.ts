import api from './index'
import type { Movie, MovieCreateRequest, MovieUpdateRequest, MovieSearchParams, RecommendationParams, ApiResponse, PageResponse } from './types'

// 获取所有电影
export const getAllMovies = () => {
  return api.get('/movies')
}

// 根据ID获取电影详情
export const getMovieById = (id: number) => {
  return api.get(`/movies/${id}`)
}

// 搜索电影
export const searchMovies = (keyword: string) => {
  return api.get(`/movies/search?keyword=${keyword}`)
}

// 根据类型获取电影
export const getMoviesByGenre = (genre: string) => {
  return api.get(`/movies/genre/${genre}`)
}

// 获取推荐电影
export const getRecommendedMovies = (userId: number) => {
  return api.get(`/movies/recommendations/${userId}`)
}

// 创建电影（管理员）
export const createMovie = (movie: Omit<Movie, 'id' | 'createdAt' | 'updatedAt' | 'averageRating'>) => {
  return api.post('/movies', movie)
}

// 更新电影（管理员）
export const updateMovie = (id: number, movie: Partial<Movie>) => {
  return api.put(`/movies/${id}`, movie)
}

// 删除电影（管理员）
export const deleteMovie = (id: number) => {
  return api.delete(`/movies/${id}`)
}