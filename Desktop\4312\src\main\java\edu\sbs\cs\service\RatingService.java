package edu.sbs.cs.service;

import edu.sbs.cs.entity.Rating;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 评分服务接口
 * 定义评分相关的业务逻辑方法
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
public interface RatingService {
    
    /**
     * 创建或更新评分
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @param score 评分
     * @return 评分实体
     */
    Rating createOrUpdateRating(Long userId, Long movieId, Double score);
    
    /**
     * 根据用户ID和电影ID查找评分
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 评分实体（可选）
     */
    Optional<Rating> findByUserIdAndMovieId(Long userId, Long movieId);
    
    /**
     * 根据用户ID获取评分列表（分页）
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 评分分页列表
     */
    Page<Rating> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据电影ID获取评分列表（分页）
     * 
     * @param movieId 电影ID
     * @param pageable 分页参数
     * @return 评分分页列表
     */
    Page<Rating> findByMovieId(Long movieId, Pageable pageable);
    
    /**
     * 计算电影的平均评分
     * 
     * @param movieId 电影ID
     * @return 平均评分
     */
    Double calculateAverageRating(Long movieId);
    
    /**
     * 获取电影的评分数量
     * 
     * @param movieId 电影ID
     * @return 评分数量
     */
    Long countByMovieId(Long movieId);
    
    /**
     * 获取用户的评分数量
     * 
     * @param userId 用户ID
     * @return 评分数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 获取评分范围内的评分列表
     * 
     * @param minScore 最小评分
     * @param maxScore 最大评分
     * @param pageable 分页参数
     * @return 评分分页列表
     */
    Page<Rating> findByScoreBetween(Double minScore, Double maxScore, Pageable pageable);
    
    /**
     * 获取用户的高评分电影列表
     * 
     * @param userId 用户ID
     * @param minScore 最小评分
     * @param pageable 分页参数
     * @return 评分分页列表
     */
    Page<Rating> findHighRatedMoviesByUser(Long userId, Double minScore, Pageable pageable);
    
    /**
     * 查找与指定用户评分相似的用户
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 相似用户的评分列表
     */
    Page<Rating> findSimilarUsers(Long userId, Pageable pageable);
    
    /**
     * 获取电影的评分分布
     * 
     * @param movieId 电影ID
     * @return 评分分布（评分 -> 数量）
     */
    Map<Double, Long> getRatingDistribution(Long movieId);
    
    /**
     * 获取用户的评分统计
     * 
     * @param userId 用户ID
     * @return 评分统计信息
     */
    Map<String, Object> getUserRatingStats(Long userId);
    
    /**
     * 删除评分
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 是否删除成功
     */
    boolean deleteRating(Long userId, Long movieId);
    
    /**
     * 根据ID删除评分
     * 
     * @param ratingId 评分ID
     * @return 是否删除成功
     */
    boolean deleteById(Long ratingId);
    
    /**
     * 获取最近的评分列表
     * 
     * @param pageable 分页参数
     * @return 最近评分分页列表
     */
    Page<Rating> getRecentRatings(Pageable pageable);
    
    /**
     * 获取用户最近的评分列表
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 用户最近评分分页列表
     */
    Page<Rating> getRecentRatingsByUser(Long userId, Pageable pageable);
    
    /**
     * 检查用户是否已对电影评分
     * 
     * @param userId 用户ID
     * @param movieId 电影ID
     * @return 是否已评分
     */
    boolean hasUserRatedMovie(Long userId, Long movieId);
    
    /**
     * 获取所有评分数量
     * 
     * @return 总评分数量
     */
    Long getTotalRatingCount();
    
    /**
     * 获取平均评分
     * 
     * @return 全局平均评分
     */
    Double getGlobalAverageRating();
    
    /**
     * 获取评分最高的电影列表
     * 
     * @param pageable 分页参数
     * @return 高评分电影评分列表
     */
    Page<Rating> getTopRatedMovies(Pageable pageable);
    
    /**
     * 获取用户的平均评分
     * 
     * @param userId 用户ID
     * @return 用户平均评分
     */
    Double getUserAverageRating(Long userId);
    
    /**
     * 批量创建评分
     * 
     * @param ratings 评分列表
     * @return 创建的评分列表
     */
    List<Rating> createRatings(List<Rating> ratings);
    
    /**
     * 根据电影ID删除所有评分
     * 
     * @param movieId 电影ID
     * @return 删除的评分数量
     */
    Long deleteByMovieId(Long movieId);
    
    /**
     * 根据用户ID删除所有评分
     * 
     * @param userId 用户ID
     * @return 删除的评分数量
     */
    Long deleteByUserId(Long userId);
}