package edu.sbs.cs;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 电影推荐系统主应用类
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableAsync
@EnableTransactionManagement
public class MovieRecommendationApplication {

    public static void main(String[] args) {
        SpringApplication.run(MovieRecommendationApplication.class, args);
        System.out.println("\n============================================");
        System.out.println("🎬 电影推荐系统启动成功！");
        System.out.println("📖 API文档地址: http://localhost:8080/swagger-ui.html");
        System.out.println("🌐 应用访问地址: http://localhost:8080");
        System.out.println("============================================\n");
    }
}