# 应用基本配置
spring.application.name=movie-recommendation-system
server.port=8080

# 数据库配置 - 使用MySQL数据库
spring.datasource.url=*************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.open-in-view=false
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# JWT配置
app.jwt.secret=movieRecommendationSystemSecretKey2025SpringWebDevelopmentCourse
app.jwt.expiration=86400000

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 跨域配置
app.cors.allowed-origins=http://localhost:3000,http://localhost:5173

# API文档配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# 日志配置
logging.level.edu.sbs.cs=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# 开发环境配置
spring.profiles.active=dev