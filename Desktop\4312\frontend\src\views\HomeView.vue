<template>
  <div class="home">
    <!-- 轮播图区域 -->
    <section class="hero-section">
      <el-carousel height="400px" indicator-position="outside">
        <el-carousel-item v-for="movie in featuredMovies" :key="movie.id">
          <div class="hero-item" :style="{ backgroundImage: `url(${movie.posterUrl})` }">
            <div class="hero-content">
              <h1>{{ movie.title }}</h1>
              <p>{{ movie.description }}</p>
              <div class="hero-actions">
                <el-button type="primary" size="large" @click="goToMovie(movie.id)">
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>

    <!-- 推荐电影区域 -->
    <section class="section">
      <div class="container">
        <h2 class="section-title">为您推荐</h2>
        <div class="movie-grid" v-if="recommendedMovies.length > 0">
          <div
            v-for="movie in recommendedMovies"
            :key="movie.id"
            class="movie-card"
            @click="goToMovie(movie.id)"
          >
            <div class="movie-poster">
              <img :src="movie.poster_url || '/placeholder-movie.svg'" :alt="movie.title" />
              <div class="movie-overlay">
                <el-icon size="24"><VideoPlay /></el-icon>
              </div>
            </div>
            <div class="movie-info">
              <h3>{{ movie.title }}</h3>
              <p class="movie-genre">{{ movie.genre }}</p>
              <div class="movie-rating">
                <el-rate
                  v-model="movie.average_rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <el-empty description="暂无推荐电影">
            <template v-if="!userStore.isLoggedIn">
              <router-link to="/login">
                <el-button type="primary">登录获取个性化推荐</el-button>
              </router-link>
            </template>
          </el-empty>
        </div>
      </div>
    </section>

    <!-- 热门电影区域 -->
    <section class="section">
      <div class="container">
        <h2 class="section-title">热门电影</h2>
        <div class="movie-grid" v-if="popularMovies.length > 0">
          <div
            v-for="movie in popularMovies"
            :key="movie.id"
            class="movie-card"
            @click="goToMovie(movie.id)"
          >
            <div class="movie-poster">
              <img :src="movie.poster_url || '/placeholder-movie.svg'" :alt="movie.title" />
              <div class="movie-overlay">
                <el-icon size="24"><VideoPlay /></el-icon>
              </div>
            </div>
            <div class="movie-info">
              <h3>{{ movie.title }}</h3>
              <p class="movie-genre">{{ movie.genre }}</p>
              <div class="movie-rating">
                <el-rate
                  v-model="movie.average_rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-else class="loading">
          <el-skeleton :rows="3" animated />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../stores/user'
import { getAllMovies, getRecommendedMovies, type Movie } from '../api/movie'

const router = useRouter()
const userStore = useUserStore()

const featuredMovies = ref<Movie[]>([])
const recommendedMovies = ref<Movie[]>([])
const popularMovies = ref<Movie[]>([])
const loading = ref(true)

// 跳转到电影详情页
const goToMovie = (id: number) => {
  router.push(`/movies/${id}`)
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    // 获取所有电影
    const moviesResponse = await getAllMovies()
    // 从分页响应中提取电影数组
    const allMovies = moviesResponse.content || []
    
    // 设置精选电影（前3部）
    featuredMovies.value = allMovies.slice(0, 3)
    
    // 设置热门电影（按评分排序）
    popularMovies.value = allMovies
      .sort((a: Movie, b: Movie) => b.average_rating - a.average_rating)
      .slice(0, 8)
    
    // 如果用户已登录，获取推荐电影
    if (userStore.isLoggedIn && userStore.user) {
      try {
        const recommended = await getRecommendedMovies(userStore.user.id)
        recommendedMovies.value = recommended.slice(0, 8)
      } catch (error) {
        console.error('Failed to load recommended movies:', error)
        // 如果推荐失败，使用热门电影作为推荐
        recommendedMovies.value = popularMovies.value.slice(0, 4)
      }
    }
  } catch (error) {
    console.error('Failed to load data:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.home {
  min-height: 100vh;
}

.hero-section {
  margin-bottom: 40px;
}

.hero-item {
  height: 400px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  max-width: 600px;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 48px;
  margin: 0 0 20px 0;
  font-weight: bold;
}

.hero-content p {
  font-size: 18px;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.section {
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 30px 0;
  color: #303133;
}

.movie-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.movie-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.movie-poster {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.movie-card:hover .movie-poster img {
  transform: scale(1.05);
}

.movie-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  color: white;
}

.movie-card:hover .movie-overlay {
  opacity: 1;
}

.movie-info {
  padding: 15px;
}

.movie-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.movie-genre {
  font-size: 14px;
  color: #909399;
  margin: 0 0 10px 0;
}

.movie-rating {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading {
  padding: 40px 0;
}

:deep(.el-carousel__indicator) {
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-carousel__indicator.is-active) {
  background-color: #409eff;
}
</style>
