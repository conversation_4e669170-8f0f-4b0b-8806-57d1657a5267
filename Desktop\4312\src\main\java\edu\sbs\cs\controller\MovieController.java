package edu.sbs.cs.controller;

import edu.sbs.cs.dto.ApiResponse;
import edu.sbs.cs.dto.MovieDTO;
import edu.sbs.cs.entity.Movie;
import edu.sbs.cs.service.MovieService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 电影控制器
 * 处理电影相关的API请求
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Tag(name = "电影管理", description = "电影相关API")
@RestController
@RequestMapping("/api/movies")
@CrossOrigin(origins = "*", maxAge = 3600)
public class MovieController {
    
    private static final Logger logger = LoggerFactory.getLogger(MovieController.class);
    
    @Autowired
    private MovieService movieService;
    
    @Operation(summary = "获取所有电影", description = "分页获取所有电影列表")
    @GetMapping
    public ResponseEntity<ApiResponse<Page<MovieDTO>>> getAllMovies(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<MovieDTO> movies = movieService.getAllMoviesDTO(pageable);
            return ResponseEntity.ok(ApiResponse.success("获取电影列表成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting movies: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取电影列表失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "根据ID获取电影", description = "根据电影ID获取电影详情")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<MovieDTO>> getMovieById(
            @Parameter(description = "电影ID") @PathVariable Long id) {
        
        try {
            Optional<Movie> movieOpt = movieService.findById(id);
            if (movieOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            Movie movie = movieOpt.get();
            MovieDTO movieDTO = movieService.convertToDTO(movie);
            
            return ResponseEntity.ok(ApiResponse.success("获取电影详情成功", movieDTO));
            
        } catch (Exception e) {
            logger.error("Error getting movie by id {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取电影详情失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "创建电影", description = "创建新电影（需要管理员权限）")
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Movie>> createMovie(@Valid @RequestBody Movie movie) {
        try {
            Movie createdMovie = movieService.createMovie(movie);
            return ResponseEntity.ok(ApiResponse.success("电影创建成功", createdMovie));
            
        } catch (Exception e) {
            logger.error("Error creating movie: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("电影创建失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "更新电影", description = "更新电影信息（需要管理员权限）")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Movie>> updateMovie(
            @Parameter(description = "电影ID") @PathVariable Long id,
            @Valid @RequestBody Movie movie) {
        
        try {
            if (!movieService.existsById(id)) {
                return ResponseEntity.notFound().build();
            }
            
            movie.setId(id);
            Movie updatedMovie = movieService.updateMovie(movie);
            return ResponseEntity.ok(ApiResponse.success("电影更新成功", updatedMovie));
            
        } catch (Exception e) {
            logger.error("Error updating movie {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("电影更新失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "删除电影", description = "删除电影（需要管理员权限）")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteMovie(
            @Parameter(description = "电影ID") @PathVariable Long id) {
        
        try {
            if (!movieService.existsById(id)) {
                return ResponseEntity.notFound().build();
            }
            
            movieService.deleteMovie(id);
            return ResponseEntity.ok(ApiResponse.success("电影删除成功"));
            
        } catch (Exception e) {
            logger.error("Error deleting movie {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("电影删除失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "搜索电影", description = "根据多个条件搜索电影")
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<MovieDTO>>> searchMovies(
            @Parameter(description = "电影标题") @RequestParam(required = false) String title,
            @Parameter(description = "导演") @RequestParam(required = false) String director,
            @Parameter(description = "电影类型") @RequestParam(required = false) Movie.Genre genre,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<MovieDTO> movies = movieService.searchMoviesDTO(title, director, genre, pageable);
            
            return ResponseEntity.ok(ApiResponse.success("搜索电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error searching movies: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("搜索电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "获取热门电影", description = "获取热门电影列表")
    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<Page<MovieDTO>>> getPopularMovies(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<MovieDTO> movies = movieService.getPopularMoviesDTO(pageable);
            
            return ResponseEntity.ok(ApiResponse.success("获取热门电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting popular movies: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取热门电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "获取最新电影", description = "获取最新上映的电影")
    @GetMapping("/latest")
    public ResponseEntity<ApiResponse<Page<MovieDTO>>> getLatestMovies(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<MovieDTO> movies = movieService.getLatestMoviesDTO(pageable);
            
            return ResponseEntity.ok(ApiResponse.success("获取最新电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting latest movies: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取最新电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "获取高评分电影", description = "获取高评分电影列表")
    @GetMapping("/high-rated")
    public ResponseEntity<ApiResponse<Page<MovieDTO>>> getHighRatedMovies(
            @Parameter(description = "最低评分") @RequestParam(defaultValue = "7.0") Double minRating,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<MovieDTO> movies = movieService.getHighRatedMoviesDTO(minRating, pageable);
            
            return ResponseEntity.ok(ApiResponse.success("获取高评分电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting high rated movies: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取高评分电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "获取推荐电影", description = "为用户推荐电影")
    @GetMapping("/recommendations")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MovieDTO>>> getRecommendedMovies(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "10") int limit) {
        
        try {
            List<MovieDTO> movies = movieService.getRecommendedMoviesDTOForUser(userId, limit);
            return ResponseEntity.ok(ApiResponse.success("获取推荐电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting recommended movies for user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取推荐电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "获取相似电影", description = "获取与指定电影相似的电影")
    @GetMapping("/{id}/similar")
    public ResponseEntity<ApiResponse<List<MovieDTO>>> getSimilarMovies(
            @Parameter(description = "电影ID") @PathVariable Long id,
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "5") int limit) {
        
        try {
            List<MovieDTO> movies = movieService.getSimilarMoviesDTO(id, limit);
            return ResponseEntity.ok(ApiResponse.success("获取相似电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting similar movies for movie {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取相似电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "根据类型获取电影", description = "根据电影类型获取电影列表")
    @GetMapping("/genre/{genre}")
    public ResponseEntity<ApiResponse<Page<MovieDTO>>> getMoviesByGenre(
            @Parameter(description = "电影类型") @PathVariable Movie.Genre genre,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "imdbRating"));
            Page<MovieDTO> movies = movieService.findByGenreDTO(genre, pageable);
            
            return ResponseEntity.ok(ApiResponse.success("获取电影成功", movies));
            
        } catch (Exception e) {
            logger.error("Error getting movies by genre {}: {}", genre, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取电影失败: " + e.getMessage()));
        }
    }
    
    @Operation(summary = "获取电影统计信息", description = "获取电影相关的统计数据")
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMovieStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalMovies", movieService.getMovieCount());
            
            // 按类型统计
            Map<String, Long> genreStats = new HashMap<>();
            for (Movie.Genre genre : Movie.Genre.values()) {
                genreStats.put(genre.name(), movieService.getMovieCountByGenre(genre));
            }
            stats.put("genreStats", genreStats);
            
            return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", stats));
            
        } catch (Exception e) {
            logger.error("Error getting movie stats: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }
}