package edu.sbs.cs.service.impl;

import edu.sbs.cs.entity.Rating;
import edu.sbs.cs.entity.User;
import edu.sbs.cs.entity.Movie;
import edu.sbs.cs.repository.RatingRepository;
import edu.sbs.cs.service.RatingService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 评分服务实现类
 * 实现评分相关的业务逻辑
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Service
@Transactional
public class RatingServiceImpl implements RatingService {
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Override
    public Rating createOrUpdateRating(Long userId, Long movieId, Double score) {
        if (userId == null || movieId == null || score == null) {
            throw new IllegalArgumentException("用户ID、电影ID和评分不能为空");
        }
        
        if (score < 1.0 || score > 5.0) {
            throw new IllegalArgumentException("评分必须在1.0到5.0之间");
        }
        
        Optional<Rating> existingRating = ratingRepository.findByUserIdAndMovieId(userId, movieId);
        
        if (existingRating.isPresent()) {
            // 更新现有评分
            Rating rating = existingRating.get();
            rating.setRating(score);
            rating.setUpdatedAt(LocalDateTime.now());
            return ratingRepository.save(rating);
        } else {
            // 创建新评分
            Rating rating = new Rating();
            // 需要先获取User和Movie实体
            User user = new User();
            user.setId(userId);
            Movie movie = new Movie();
            movie.setId(movieId);
            rating.setUser(user);
            rating.setMovie(movie);
            rating.setRating(score);
            rating.setCreatedAt(LocalDateTime.now());
            rating.setUpdatedAt(LocalDateTime.now());
            return ratingRepository.save(rating);
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<Rating> findByUserIdAndMovieId(Long userId, Long movieId) {
        if (userId == null || movieId == null) {
            throw new IllegalArgumentException("用户ID和电影ID不能为空");
        }
        return ratingRepository.findByUserIdAndMovieId(userId, movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> findByUserId(Long userId, Pageable pageable) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return ratingRepository.findByUserId(userId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> findByMovieId(Long movieId, Pageable pageable) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return ratingRepository.findByMovieId(movieId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Double calculateAverageRating(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        Double average = ratingRepository.findAverageRatingByMovieId(movieId);
        return average != null ? Math.round(average * 10.0) / 10.0 : 0.0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long countByMovieId(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        return ratingRepository.countByMovieId(movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long countByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return ratingRepository.countByUserId(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> findByScoreBetween(Double minScore, Double maxScore, Pageable pageable) {
        if (minScore == null || maxScore == null) {
            throw new IllegalArgumentException("最小评分和最大评分不能为空");
        }
        if (minScore > maxScore) {
            throw new IllegalArgumentException("最小评分不能大于最大评分");
        }
        return ratingRepository.findByScoreBetween(minScore, maxScore, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> findHighRatedMoviesByUser(Long userId, Double minScore, Pageable pageable) {
        if (userId == null || minScore == null) {
            throw new IllegalArgumentException("用户ID和最小评分不能为空");
        }
        return ratingRepository.findHighRatedMoviesByUser(userId, minScore, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> findSimilarUsers(Long userId, Pageable pageable) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return ratingRepository.findSimilarUsers(userId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<Double, Long> getRatingDistribution(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        
        List<Object[]> distribution = ratingRepository.findRatingDistributionByMovieId(movieId);
        Map<Double, Long> result = new HashMap<>();
        
        for (Object[] row : distribution) {
            Double score = (Double) row[0];
            Long count = (Long) row[1];
            result.put(score, count);
        }
        
        return result;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserRatingStats(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总评分数
        Long totalRatings = ratingRepository.countByUserId(userId);
        stats.put("totalRatings", totalRatings);
        
        if (totalRatings > 0) {
            // 平均评分
            Double averageRating = ratingRepository.findAverageRatingByUserId(userId);
            stats.put("averageRating", averageRating != null ? Math.round(averageRating * 10.0) / 10.0 : 0.0);
            
            // 最高评分
            Double maxRating = ratingRepository.findMaxRatingByUserId(userId);
            stats.put("maxRating", maxRating != null ? maxRating : 0.0);
            
            // 最低评分
            Double minRating = ratingRepository.findMinRatingByUserId(userId);
            stats.put("minRating", minRating != null ? minRating : 0.0);
        } else {
            stats.put("averageRating", 0.0);
            stats.put("maxRating", 0.0);
            stats.put("minRating", 0.0);
        }
        
        return stats;
    }
    
    @Override
    public boolean deleteRating(Long userId, Long movieId) {
        if (userId == null || movieId == null) {
            throw new IllegalArgumentException("用户ID和电影ID不能为空");
        }
        
        Optional<Rating> rating = ratingRepository.findByUserIdAndMovieId(userId, movieId);
        if (rating.isPresent()) {
            ratingRepository.delete(rating.get());
            return true;
        }
        return false;
    }
    
    @Override
    public boolean deleteById(Long ratingId) {
        if (ratingId == null) {
            throw new IllegalArgumentException("评分ID不能为空");
        }
        
        if (ratingRepository.existsById(ratingId)) {
            ratingRepository.deleteById(ratingId);
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> getRecentRatings(Pageable pageable) {
        return ratingRepository.findRecentRatings(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> getRecentRatingsByUser(Long userId, Pageable pageable) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return ratingRepository.findRecentRatingsByUser(userId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean hasUserRatedMovie(Long userId, Long movieId) {
        if (userId == null || movieId == null) {
            throw new IllegalArgumentException("用户ID和电影ID不能为空");
        }
        return ratingRepository.existsByUserIdAndMovieId(userId, movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long getTotalRatingCount() {
        return ratingRepository.count();
    }
    
    @Override
    @Transactional(readOnly = true)
    public Double getGlobalAverageRating() {
        Double average = ratingRepository.findGlobalAverageRating();
        return average != null ? Math.round(average * 10.0) / 10.0 : 0.0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Rating> getTopRatedMovies(Pageable pageable) {
        return ratingRepository.findTopRatedMovieRatings(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Double getUserAverageRating(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        Double average = ratingRepository.findAverageRatingByUserId(userId);
        return average != null ? Math.round(average * 10.0) / 10.0 : 0.0;
    }
    
    @Override
    public List<Rating> createRatings(List<Rating> ratings) {
        if (ratings == null || ratings.isEmpty()) {
            throw new IllegalArgumentException("评分列表不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (Rating rating : ratings) {
            if (rating.getCreatedAt() == null) {
                rating.setCreatedAt(now);
            }
            if (rating.getUpdatedAt() == null) {
                rating.setUpdatedAt(now);
            }
        }
        
        return ratingRepository.saveAll(ratings);
    }
    
    @Override
    public Long deleteByMovieId(Long movieId) {
        if (movieId == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        Long count = ratingRepository.countByMovieId(movieId);
        ratingRepository.deleteByMovieId(movieId);
        return count;
    }
    
    @Override
    public Long deleteByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        Long count = ratingRepository.countByUserId(userId);
        ratingRepository.deleteByUserId(userId);
        return count;
    }
}