import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
    },
    {
    path: '/movies',
    name: 'movies',
    component: () => import('../views/MoviesView.vue'),
    meta: { requiresAuth: true }
  },
    {
      path: '/movies/:id',
      name: 'movie-detail',
      component: () => import('../views/MovieDetailView.vue'),
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  let user = {}
  
  try {
    const savedUser = localStorage.getItem('user')
    if (savedUser && savedUser !== 'undefined' && savedUser !== 'null') {
      user = JSON.parse(savedUser)
    }
  } catch (error) {
    console.error('Failed to parse user data in router guard:', error)
    // 清理无效数据
    localStorage.removeItem('user')
    localStorage.removeItem('token')
  }
  
  // 如果已登录且访问登录/注册页面，重定向到首页
  if (token && (to.name === 'login' || to.name === 'register')) {
    next('/')
    return
  }
  
  // 需要认证的页面
  if (to.meta.requiresAuth && !token) {
    next('/login')
    return
  }
  
  // 需要管理员权限的页面
  if (to.meta.requiresAdmin && (user as any).role !== 'ADMIN') {
    next('/')
    return
  }
  
  next()
})

export default router
