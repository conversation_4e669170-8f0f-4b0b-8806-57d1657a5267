# 电影推荐系统配置文件
spring:
  # 应用基本信息
  application:
    name: movie-recommendation-system
  
  # 数据源配置
  datasource:
    url: ***************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: root
  
  # JPA 配置
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
  
  # 数据初始化
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: false
  
  # Jackson 配置
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# JWT 配置
jwt:
  secret: movieRecommendationSystemSecretKey2025ForJWTTokenGeneration
  expiration: 86400000  # 24小时（毫秒）
  refresh-expiration: 604800000  # 7天（毫秒）

# 日志配置
logging:
  level:
    edu.sbs.cs: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/movie-recommendation.log

# Swagger/OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: method
    tags-sorter: alpha
    try-it-out-enabled: true
  show-actuator: false

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: 电影推荐系统
    description: 基于Spring Boot的电影推荐系统
    version: 1.0.0
    author: Movie Recommendation Team
  build:
    java-version: ${java.version}
    spring-boot-version: ${spring-boot.version}

# 自定义配置
app:
  # 分页配置
  pagination:
    default-page-size: 10
    max-page-size: 100
  
  # 推荐算法配置
  recommendation:
    similar-users-count: 10
    similar-movies-count: 10
    min-rating-count: 5
  
  # 缓存配置
  cache:
    ttl: 3600  # 1小时
    max-size: 1000
  
  # 安全配置
  security:
    password-strength:
      min-length: 6
      max-length: 20
    rate-limit:
      requests-per-minute: 60
  
  # 文件存储配置
  file:
    upload-dir: uploads/
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif

# 开发环境特定配置
---
spring:
  config:
    activate:
      on-profile: dev
  
  # 开发环境数据库配置
  datasource:
    url: jdbc:h2:mem:moviedb_dev
  
  # 开发环境JPA配置
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop

# 日志级别（开发环境）
logging:
  level:
    root: INFO
    edu.sbs.cs: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  # 生产环境数据库配置（示例：MySQL）
  # datasource:
  #   url: *******************************************************************************************************
  #   driver-class-name: com.mysql.cj.jdbc.Driver
  #   username: ${DB_USERNAME:movie_user}
  #   password: ${DB_PASSWORD:movie_password}
  
  # 生产环境JPA配置
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

# 生产环境日志配置
logging:
  level:
    root: WARN
    edu.sbs.cs: INFO
  file:
    name: /var/log/movie-recommendation/application.log

# 生产环境JWT配置
jwt:
  secret: ${JWT_SECRET:productionSecretKeyForMovieRecommendationSystem2025}
  expiration: ${JWT_EXPIRATION:86400000}

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  # 测试环境数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
  
  # 测试环境JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  
  # 禁用数据初始化
  sql:
    init:
      mode: never

# 测试环境日志配置
logging:
  level:
    root: WARN
    edu.sbs.cs: INFO