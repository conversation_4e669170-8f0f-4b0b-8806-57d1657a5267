package edu.sbs.cs.controller;

import edu.sbs.cs.dto.ApiResponse;
import edu.sbs.cs.entity.Rating;
import edu.sbs.cs.service.RatingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.Map;
import java.util.Optional;

/**
 * 评分控制器
 * 处理评分相关的API请求
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@RestController
@RequestMapping("/api/ratings")
@Tag(name = "评分管理", description = "电影评分相关接口")
public class RatingController {
    
    @Autowired
    private RatingService ratingService;
    
    /**
     * 创建或更新评分
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "创建或更新评分", description = "用户对电影进行评分")
    public ResponseEntity<ApiResponse<Rating>> createOrUpdateRating(
            @Parameter(description = "电影ID", required = true) @RequestParam Long movieId,
            @Parameter(description = "评分(1.0-5.0)", required = true) 
            @RequestParam @DecimalMin(value = "1.0", message = "评分不能低于1.0") 
            @DecimalMax(value = "5.0", message = "评分不能高于5.0") Double score,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        Rating rating = ratingService.createOrUpdateRating(userId, movieId, score);
        
        return ResponseEntity.ok(ApiResponse.success("评分成功", rating));
    }
    
    /**
     * 获取用户对特定电影的评分
     */
    @GetMapping("/user/{userId}/movie/{movieId}")
    @Operation(summary = "获取用户对电影的评分", description = "查询用户对特定电影的评分")
    public ResponseEntity<ApiResponse<Rating>> getUserMovieRating(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId) {
        
        Optional<Rating> rating = ratingService.findByUserIdAndMovieId(userId, movieId);
        
        if (rating.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("获取评分成功", rating.get()));
        } else {
            return ResponseEntity.ok(ApiResponse.success("用户未对该电影评分", null));
        }
    }
    
    /**
     * 获取用户的所有评分
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户的所有评分", description = "分页查询用户的评分列表")
    public ResponseEntity<ApiResponse<Page<Rating>>> getUserRatings(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size,
            @Parameter(description = "排序字段", example = "createdAt") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向", example = "desc") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Rating> ratings = ratingService.findByUserId(userId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户评分列表成功", ratings));
    }
    
    /**
     * 获取电影的所有评分
     */
    @GetMapping("/movie/{movieId}")
    @Operation(summary = "获取电影的所有评分", description = "分页查询电影的评分列表")
    public ResponseEntity<ApiResponse<Page<Rating>>> getMovieRatings(
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size,
            @Parameter(description = "排序字段", example = "createdAt") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向", example = "desc") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Rating> ratings = ratingService.findByMovieId(movieId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取电影评分列表成功", ratings));
    }
    
    /**
     * 获取电影的平均评分
     */
    @GetMapping("/movie/{movieId}/average")
    @Operation(summary = "获取电影平均评分", description = "计算电影的平均评分")
    public ResponseEntity<ApiResponse<Double>> getMovieAverageRating(
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId) {
        
        Double averageRating = ratingService.calculateAverageRating(movieId);
        
        return ResponseEntity.ok(ApiResponse.success("获取平均评分成功", averageRating));
    }
    
    /**
     * 获取电影的评分统计
     */
    @GetMapping("/movie/{movieId}/stats")
    @Operation(summary = "获取电影评分统计", description = "获取电影的评分统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMovieRatingStats(
            @Parameter(description = "电影ID", required = true) @PathVariable Long movieId) {
        
        Double averageRating = ratingService.calculateAverageRating(movieId);
        Long ratingCount = ratingService.countByMovieId(movieId);
        Map<Double, Long> distribution = ratingService.getRatingDistribution(movieId);
        
        Map<String, Object> stats = Map.of(
            "averageRating", averageRating,
            "ratingCount", ratingCount,
            "distribution", distribution
        );
        
        return ResponseEntity.ok(ApiResponse.success("获取评分统计成功", stats));
    }
    
    /**
     * 获取用户的评分统计
     */
    @GetMapping("/user/{userId}/stats")
    @Operation(summary = "获取用户评分统计", description = "获取用户的评分统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserRatingStats(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        Map<String, Object> stats = ratingService.getUserRatingStats(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户评分统计成功", stats));
    }
    
    /**
     * 获取评分范围内的评分列表
     */
    @GetMapping("/range")
    @Operation(summary = "按评分范围查询", description = "查询指定评分范围内的评分")
    public ResponseEntity<ApiResponse<Page<Rating>>> getRatingsByRange(
            @Parameter(description = "最小评分", required = true) @RequestParam @DecimalMin("1.0") Double minScore,
            @Parameter(description = "最大评分", required = true) @RequestParam @DecimalMax("5.0") Double maxScore,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Rating> ratings = ratingService.findByScoreBetween(minScore, maxScore, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取评分范围列表成功", ratings));
    }
    
    /**
     * 获取用户的高评分电影
     */
    @GetMapping("/user/{userId}/high-rated")
    @Operation(summary = "获取用户高评分电影", description = "查询用户评分较高的电影")
    public ResponseEntity<ApiResponse<Page<Rating>>> getUserHighRatedMovies(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "最小评分", example = "4.0") @RequestParam(defaultValue = "4.0") @DecimalMin("1.0") Double minScore,
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("score").descending());
        Page<Rating> ratings = ratingService.findHighRatedMoviesByUser(userId, minScore, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户高评分电影成功", ratings));
    }
    
    /**
     * 获取最近的评分
     */
    @GetMapping("/recent")
    @Operation(summary = "获取最近评分", description = "查询最近的评分记录")
    public ResponseEntity<ApiResponse<Page<Rating>>> getRecentRatings(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Rating> ratings = ratingService.getRecentRatings(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取最近评分成功", ratings));
    }
    
    /**
     * 获取评分最高的电影
     */
    @GetMapping("/top-rated")
    @Operation(summary = "获取高评分电影", description = "查询评分最高的电影")
    public ResponseEntity<ApiResponse<Page<Rating>>> getTopRatedMovies(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Rating> ratings = ratingService.getTopRatedMovies(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取高评分电影成功", ratings));
    }
    
    /**
     * 删除评分
     */
    @DeleteMapping
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "删除评分", description = "用户删除自己的评分")
    public ResponseEntity<ApiResponse<String>> deleteRating(
            @Parameter(description = "电影ID", required = true) @RequestParam Long movieId,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        boolean deleted = ratingService.deleteRating(userId, movieId);
        
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success("删除评分成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.error("评分不存在或删除失败"));
        }
    }
    
    /**
     * 检查用户是否已评分
     */
    @GetMapping("/check")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "检查是否已评分", description = "检查用户是否已对电影评分")
    public ResponseEntity<ApiResponse<Boolean>> checkUserRating(
            @Parameter(description = "电影ID", required = true) @RequestParam Long movieId,
            Authentication authentication) {
        
        Long userId = Long.parseLong(authentication.getName());
        boolean hasRated = ratingService.hasUserRatedMovie(userId, movieId);
        
        return ResponseEntity.ok(ApiResponse.success("检查评分状态成功", hasRated));
    }
    
    /**
     * 获取全局评分统计
     */
    @GetMapping("/global-stats")
    @Operation(summary = "获取全局评分统计", description = "获取系统整体的评分统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getGlobalRatingStats() {
        
        Long totalRatings = ratingService.getTotalRatingCount();
        Double globalAverage = ratingService.getGlobalAverageRating();
        
        Map<String, Object> stats = Map.of(
            "totalRatings", totalRatings,
            "globalAverageRating", globalAverage
        );
        
        return ResponseEntity.ok(ApiResponse.success("获取全局评分统计成功", stats));
    }
    
    /**
     * 管理员删除评分
     */
    @DeleteMapping("/{ratingId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "管理员删除评分", description = "管理员根据ID删除评分")
    public ResponseEntity<ApiResponse<String>> deleteRatingById(
            @Parameter(description = "评分ID", required = true) @PathVariable Long ratingId) {
        
        boolean deleted = ratingService.deleteById(ratingId);
        
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success("删除评分成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.error("评分不存在或删除失败"));
        }
    }
}