package edu.sbs.cs.service.impl;

import edu.sbs.cs.dto.MovieDTO;
import edu.sbs.cs.entity.Movie;
import edu.sbs.cs.repository.MovieRepository;
import edu.sbs.cs.repository.RatingRepository;
import edu.sbs.cs.service.MovieService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 电影服务实现类
 * 
 * <AUTHOR> Recommendation System
 * @version 1.0
 * @since 2025-01-01
 */
@Service
@Transactional
public class MovieServiceImpl implements MovieService {
    
    private static final Logger logger = LoggerFactory.getLogger(MovieServiceImpl.class);
    
    @Autowired
    private MovieRepository movieRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Override
    public Movie createMovie(Movie movie) {
        logger.info("Creating new movie: {}", movie.getTitle());
        Movie savedMovie = movieRepository.save(movie);
        logger.info("Movie created successfully: {}", savedMovie.getTitle());
        return savedMovie;
    }
    
    @Override
    public Movie updateMovie(Movie movie) {
        logger.info("Updating movie: {}", movie.getTitle());
        
        if (!movieRepository.existsById(movie.getId())) {
            throw new RuntimeException("电影不存在: " + movie.getId());
        }
        
        Movie updatedMovie = movieRepository.save(movie);
        logger.info("Movie updated successfully: {}", updatedMovie.getTitle());
        return updatedMovie;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<Movie> findById(Long id) {
        return movieRepository.findById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Movie> findByTitle(String title) {
        return movieRepository.findByTitleContainingIgnoreCase(title);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> findByDirector(String director, Pageable pageable) {
        return movieRepository.findByDirectorContainingIgnoreCase(director, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> findByGenre(Movie.Genre genre, Pageable pageable) {
        return movieRepository.findByGenre(genre, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> findByReleaseDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return movieRepository.findByReleaseDateBetween(startDate, endDate, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> searchMovies(String title, String director, Movie.Genre genre, Pageable pageable) {
        return movieRepository.findByMultipleConditions(title, director, genre, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> getPopularMovies(Pageable pageable) {
        return movieRepository.findPopularMovies(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> getLatestMovies(Pageable pageable) {
        return movieRepository.findByOrderByReleaseDateDesc(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> getHighRatedMovies(Double minRating, Pageable pageable) {
        return movieRepository.findHighRatedMovies(minRating, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Movie> getRecommendedMoviesByGenre(Movie.Genre genre, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<Movie> page = movieRepository.findRecommendedMoviesByGenre(genre, pageable);
        return page.getContent();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Movie> getRecommendedMoviesForUser(Long userId, int limit) {
        return movieRepository.findRecommendedMoviesForUser(userId, limit);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> getAllMovies(Pageable pageable) {
        return movieRepository.findAll(pageable);
    }
    
    @Override
    public void deleteMovie(Long id) {
        logger.info("Deleting movie ID: {}", id);
        
        if (!movieRepository.existsById(id)) {
            throw new RuntimeException("电影不存在: " + id);
        }
        
        movieRepository.deleteById(id);
        logger.info("Movie deleted successfully: {}", id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getMovieCount() {
        return movieRepository.count();
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getMovieCountByGenre(Movie.Genre genre) {
        return movieRepository.countByGenre(genre);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> getMoviesWithRatings(Pageable pageable) {
        return movieRepository.findMoviesWithRatings(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Double getAverageRating(Long movieId) {
        return ratingRepository.findAverageRatingByMovieId(movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long getRatingCount(Long movieId) {
        return ratingRepository.countByMovieId(movieId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return movieRepository.existsById(id);
    }
    
    @Override
    public List<Movie> createMovies(List<Movie> movies) {
        logger.info("Creating {} movies in batch", movies.size());
        List<Movie> savedMovies = movieRepository.saveAll(movies);
        logger.info("Batch movie creation completed: {} movies", savedMovies.size());
        return savedMovies;
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Movie> getSimilarMovies(Long movieId, int limit) {
        Optional<Movie> movieOpt = movieRepository.findById(movieId);
        if (movieOpt.isEmpty()) {
            throw new RuntimeException("电影不存在: " + movieId);
        }
        
        Movie movie = movieOpt.get();
        
        // 基于类型推荐相似电影
        Pageable pageable = PageRequest.of(0, limit + 1, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<Movie> page = movieRepository.findRecommendedMoviesByGenre(movie.getGenre(), pageable);
        List<Movie> similarMovies = page.getContent();
        
        // 移除当前电影
        similarMovies.removeIf(m -> m.getId().equals(movieId));
        
        // 确保返回的数量不超过限制
        if (similarMovies.size() > limit) {
            similarMovies = similarMovies.subList(0, limit);
        }
        
        return similarMovies;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Movie> getUserWatchHistory(Long userId, Pageable pageable) {
        // 基于用户的评分记录获取观看历史
        return movieRepository.findMoviesByUserRatings(userId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public MovieDTO convertToDTO(Movie movie) {
        if (movie == null) {
            return null;
        }
        
        // 获取评分信息
        Double averageRating = getAverageRating(movie.getId());
        Long ratingCount = getRatingCount(movie.getId());
        
        return new MovieDTO(movie, averageRating, ratingCount);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MovieDTO> getAllMoviesDTO(Pageable pageable) {
        Page<Movie> moviePage = movieRepository.findAll(pageable);
        return moviePage.map(this::convertToDTO);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MovieDTO> searchMoviesDTO(String title, String director, Movie.Genre genre, Pageable pageable) {
        Page<Movie> moviePage = searchMovies(title, director, genre, pageable);
        return moviePage.map(this::convertToDTO);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MovieDTO> getPopularMoviesDTO(Pageable pageable) {
        Page<Movie> moviePage = getPopularMovies(pageable);
        return moviePage.map(this::convertToDTO);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MovieDTO> getLatestMoviesDTO(Pageable pageable) {
        Page<Movie> moviePage = getLatestMovies(pageable);
        return moviePage.map(this::convertToDTO);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MovieDTO> getHighRatedMoviesDTO(Double minRating, Pageable pageable) {
        Page<Movie> moviePage = getHighRatedMovies(minRating, pageable);
        return moviePage.map(this::convertToDTO);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MovieDTO> getRecommendedMoviesDTOForUser(Long userId, int limit) {
        List<Movie> movies = getRecommendedMoviesForUser(userId, limit);
        return movies.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MovieDTO> getSimilarMoviesDTO(Long movieId, int limit) {
        List<Movie> movies = getSimilarMovies(movieId, limit);
        return movies.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MovieDTO> findByGenreDTO(Movie.Genre genre, Pageable pageable) {
        Page<Movie> moviePage = findByGenre(genre, pageable);
        return moviePage.map(this::convertToDTO);
    }
}